-- Test script to verify the trust score trigger works correctly
-- This can be run in the database to test the trigger functionality

-- Test 1: Verify the trigger function exists
SELECT proname, prosrc FROM pg_proc WHERE proname = 'update_trust_score_on_identity_change';

-- Test 2: Verify the trigger exists
SELECT tgname, tgrelid::regclass, tgfoid::regproc 
FROM pg_trigger 
WHERE tgname = 'trigger_update_trust_score_on_identity_change';

-- Test 3: Check current state of test2 user
SELECT id, username, identity_verified, identity_verification_status, trust_score 
FROM users 
WHERE username = 'test2';

-- Test 4: Simulate identity verification change (if needed for testing)
-- UNCOMMENT ONLY IF YOU WANT TO TEST THE TRIGGER:
-- UPDATE users 
-- SET identity_verified = true 
-- WHERE username = 'test2' AND identity_verified = false;

-- Test 5: Check if the one-time fix was applied
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN identity_verified = true THEN 1 END) as verified_users,
    COUNT(CASE WHEN identity_verified = true AND trust_score >= 70 THEN 1 END) as verified_with_bonus
FROM users;

-- Test 6: Find any users who might still need the fix
SELECT id, username, identity_verified, identity_verification_status, trust_score,
       CASE 
           WHEN identity_verified = true AND trust_score < 70 THEN 'NEEDS FIX'
           WHEN identity_verified = true AND trust_score >= 70 THEN 'OK'
           ELSE 'NOT VERIFIED'
       END as status
FROM users 
ORDER BY id;
