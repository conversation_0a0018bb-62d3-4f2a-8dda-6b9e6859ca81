import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface SellerVerificationStatus {
  isVerified: boolean; // This means user has a seller account, not identity verification
  hasIdentityVerification?: boolean; // This will track actual identity verification
  trustScore: number;
  tier: 'New Seller' | 'Verified' | 'Trusted' | 'Premium';
  maxListings: number;
  platformFee: string;
  canListInSafeSphere: boolean;
  isGuest: boolean;
  verificationInProgress?: boolean;
}

const SellPage = () => {
  const { user: activeUser } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<SellerVerificationStatus | null>(null);
  const [hasCompletedSale, setHasCompletedSale] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    quantity: '1',
    category: '',
    tags: '',
    shipping: '',
    image: null as File | null,
    imagePreview: ''
  });

  // Check if user has completed sales
  useEffect(() => {
    const checkUserSalesStatus = async () => {
      if (!activeUser) {
        setHasCompletedSale(false);
        return;
      }

      try {
        // Check for completed sales - for now we'll use a simple heuristic
        // In a real implementation, you'd have a separate endpoint for completed sales
        try {
          const salesResponse = await apiRequest('/api/sales/completed', {
            credentials: 'include',
          });
          setHasCompletedSale(salesResponse.data?.length > 0);
        } catch (salesError) {
          // If sales endpoint doesn't exist, fall back to checking if user has listings
          // This is a temporary solution until proper sales tracking is implemented
          console.log('Sales endpoint not available, using listings as proxy');
          setHasCompletedSale(false);
        }
      } catch (error) {
        console.error('Error checking user sales status:', error);
        setHasCompletedSale(false);
      }
    };

    checkUserSalesStatus();
  }, [activeUser]);

  useEffect(() => {
    const defaultStatus = {
      isVerified: false,
      trustScore: 0,
      tier: 'New Seller' as const,
      maxListings: 5, // Default limit for new/unverified sellers
      platformFee: '5%', // Default platform fee for unverified sellers
      canListInSafeSphere: false, // Can only list in OpenSphere
      isGuest: !activeUser,
      verificationInProgress: false
    };

    const fetchSellerStatus = async () => {
      try {
        const response = await apiRequest(`/api/seller/status?t=${Date.now()}`, {
          credentials: 'include',
        });

        // Update with server data if available
        console.log('Seller status API response:', response);
        const newStatus = {
          ...defaultStatus,
          ...response
        };
        console.log('Final verification status:', newStatus);
        setVerificationStatus(newStatus);
      } catch (error) {
        console.error('Error fetching seller status:', error);
        // Use default status but show error for authenticated users
        if (activeUser) {
          toast({
            title: 'Error',
            description: 'Failed to load seller information. Using default settings.',
            variant: 'destructive',
          });
        }
        setVerificationStatus(defaultStatus);
      } finally {
        setIsLoading(false);
      }
    };

    if (activeUser) {
      fetchSellerStatus();

      // Set up periodic refresh to catch identity verification updates
      const refreshInterval = setInterval(() => {
        fetchSellerStatus();
      }, 30000); // Refresh every 30 seconds

      // Also refresh when the page becomes visible (user returns to tab)
      const handleVisibilityChange = () => {
        if (!document.hidden) {
          fetchSellerStatus();
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Cleanup interval and event listener
      return () => {
        clearInterval(refreshInterval);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    } else {
      // Set default values for unauthenticated users
      setVerificationStatus(defaultStatus);
      setIsLoading(false);
    }
  }, [activeUser, toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStartVerification = () => {
    if (!activeUser) {
      // For guests, toggle verification form
      setVerificationStatus(prev => ({
        ...prev!,
        verificationInProgress: true,
        accountCreationInProgress: false
      }));
    } else {
      // For logged-in users, redirect to verification page
      window.location.href = '/verify-account';
    }
  };

  const handleRefreshStatus = async () => {
    if (!activeUser) return;

    setIsLoading(true);
    try {
      const response = await apiRequest(`/api/seller/status?t=${Date.now()}`, {
        credentials: 'include',
      });

      const newStatus = {
        isVerified: false,
        trustScore: 0,
        tier: 'New Seller' as const,
        maxListings: 5,
        platformFee: '5%',
        canListInSafeSphere: false,
        isGuest: !activeUser,
        verificationInProgress: false,
        ...response
      };

      setVerificationStatus(newStatus);

      toast({
        title: 'Status Updated',
        description: 'Your seller status has been refreshed.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to refresh status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartAccountCreation = () => {
    // Redirect to signup page instead of showing seller account creation form
    window.location.href = '/auth?mode=signup';
  };

  const scrollToItemForm = () => {
    const formElement = document.getElementById('item-details-form');
    if (formElement) {
      const yOffset = -20; // Adjust this value as needed
      const y = formElement.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  const handleGuestVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would collect verification info and submit to your API
    // For now, we'll simulate a successful verification
    setVerificationStatus(prev => ({
      ...prev!,
      isVerified: true,
      trustScore: 70, // 30 (account) + 40 (verification) = 70 points
      tier: 'Verified',
      maxListings: 20,
      platformFee: '3.5%',
      canListInSafeSphere: false,
      isGuest: true,
      verificationInProgress: false
    }));

    toast({
      title: 'Verification Started',
      description: 'Your verification is being processed. This may take up to 24 hours.',
      variant: 'default',
    });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({
        ...prev,
        image: file,
        imagePreview: URL.createObjectURL(file)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // For unauthenticated users, we'll create a guest listing
    // For authenticated users, we'll use their account

    // If user is not logged in, show a toast about guest listing
    if (!activeUser) {
      toast({
        title: 'Guest Listing',
        description: 'You are creating a guest listing. Some features may be limited.',
        variant: 'default',
      });
    }

    // If user is logged in but not verified, they can still list in OpenSphere
    if (activeUser && !verificationStatus?.isVerified) {
      toast({
        title: 'Limited Access',
        description: 'As an unverified seller, you can list items in OpenSphere only.',
        variant: 'default',
      });
    }

    setIsSubmitting(true);

    try {
      const formDataToSend = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formDataToSend.append(key, value as string | Blob);
        }
      });

      await apiRequest('/api/products', {
        method: 'POST',
        body: formDataToSend,
        credentials: 'include',
      });

      toast({
        title: 'Success!',
        description: 'Your item has been listed successfully!',
      });

      // Reset form
      setFormData({
        title: '',
        description: '',
        price: '',
        quantity: '1',
        category: '',
        tags: '',
        shipping: '',
        image: null,
        imagePreview: ''
      });

    } catch (error) {
      console.error('Error listing item:', error);
      toast({
        title: 'Error',
        description: 'Failed to list item. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !verificationStatus) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Show the main form with verification status


  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">List an Item for Sale</h1>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <span>Seller Tier: <Badge variant="outline">{verificationStatus.tier}</Badge></span>
            <span>•</span>
            <span>Trust Score: {verificationStatus.trustScore}/100</span>
            <span>•</span>
            <span>Max Listings: {verificationStatus.maxListings}</span>
            <span>•</span>
            <span>Platform Fee: {verificationStatus.platformFee}</span>
          </div>
          {activeUser && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshStatus}
              disabled={isLoading}
              className="ml-4"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh Status'}
            </Button>
          )}
        </div>
        <Progress value={verificationStatus.trustScore} className="h-2 mt-2" />
      </div>

      {/* Reputation Box - Only show if there are incomplete trust point opportunities */}
      {(!activeUser || !verificationStatus?.hasIdentityVerification || !hasCompletedSale) && (
        <div className="mb-6 border rounded-lg overflow-hidden">
          {!activeUser && (
          <div className="bg-yellow-50 border-b border-yellow-200 p-3 text-sm text-yellow-700 flex items-center">
            <svg className="h-5 w-5 text-yellow-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span>Guest users can only receive payments in DasWos Coins. <a href="#seller-account" className="font-medium text-yellow-700 hover:text-yellow-600 underline">Create a seller account</a> to unlock multiple payment methods and selling features.</span>
          </div>
        )}
        <div className="bg-gradient-to-r from-blue-50 to-yellow-50 p-4 border-b">
          <h3 className="font-medium text-gray-900 text-lg flex items-center mb-2">
            <svg className="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            Build Your Seller Reputation
          </h3>
          <p className="text-sm text-gray-600">
            Earn trust points by completing these steps and unlock more benefits
          </p>

          <div className="mt-4 flex flex-wrap gap-2">
            {/* Create Account - Only show if user is not logged in */}
            {!activeUser && (
              <div className="flex-1 min-w-[200px] p-3 rounded-md border bg-white border-gray-200">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">Create Account</span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    +30 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-gray-300" style={{ width: '0%' }}></div>
                </div>
                <div className="mt-2">
                  <button
                    onClick={handleStartAccountCreation}
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Create DasWos account
                  </button>
                </div>
              </div>
            )}

            {/* Account Created - Show completion status if user is logged in */}
            {activeUser && (
              <div className="flex-1 min-w-[200px] p-3 rounded-md border bg-green-50 border-green-200">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">✓ Account Created</span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    +30 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500" style={{ width: '100%' }}></div>
                </div>
                <div className="mt-2 text-xs text-green-600">
                  Completed
                </div>
              </div>
            )}

            {/* Identity Verification Checkpoint - Single checkpoint with dynamic states */}
            {(!activeUser || (activeUser && verificationStatus && !verificationStatus.hasIdentityVerification)) && (
              <div className={`flex-1 min-w-[200px] p-3 rounded-md border ${
                activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved'
                  ? 'bg-yellow-50 border-yellow-200'
                  : 'bg-white border-gray-200'
              }`}>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">
                    {activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved'
                      ? '⏳ Waiting for Approval'
                      : 'Verify Identity'
                    }
                  </span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    +40 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className={`h-full ${
                    activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved'
                      ? 'bg-yellow-400'
                      : 'bg-gray-300'
                  }`} style={{
                    width: activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved'
                      ? '75%'
                      : '0%'
                  }}></div>
                </div>
                <div className="mt-2">
                  {activeUser && activeUser.identityVerified === true && activeUser.identityVerificationStatus !== 'approved' ? (
                    <div className="text-xs text-yellow-600">
                      Waiting for approval
                    </div>
                  ) : (
                    <button
                      onClick={handleStartVerification}
                      className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {activeUser ? 'Start verification' : 'Verify identity to sell'}
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Identity Verified - Show completion status if user has completed identity verification */}
            {activeUser && verificationStatus?.hasIdentityVerification && (
              <div className="flex-1 min-w-[200px] p-3 rounded-md border bg-green-50 border-green-200">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">✓ Identity Verified</span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    +40 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500" style={{ width: '100%' }}></div>
                </div>
                <div className="mt-2 text-xs text-green-600">
                  Completed
                </div>
              </div>
            )}

            {/* First Sale - Only show if user hasn't completed a sale yet */}
            {!hasCompletedSale && (
              <div className="flex-1 min-w-[200px] p-3 rounded-md border bg-white border-gray-200">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">First Sale</span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    +5 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-gray-300" style={{ width: '0%' }}></div>
                </div>
                <div className="mt-2">
                  <button
                    onClick={scrollToItemForm}
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Complete your first sale
                  </button>
                </div>
              </div>
            )}

            {/* First Sale Completed - Show completion status */}
            {hasCompletedSale && (
              <div className="flex-1 min-w-[200px] p-3 rounded-md border bg-green-50 border-green-200">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">✓ First Sale</span>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    +5 points
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500" style={{ width: '100%' }}></div>
                </div>
                <div className="mt-2 text-xs text-green-600">
                  Completed
                </div>
              </div>
            )}
          </div>
        </div>

        {verificationStatus?.verificationInProgress && (
          <div className="p-6 bg-white border-t">
            <h4 className="font-medium text-gray-900 mb-4">Verify Your Identity</h4>
            <p className="text-sm text-gray-600 mb-4">
              Complete verification to increase your trust score and unlock more selling benefits.
            </p>

            <form onSubmit={handleGuestVerification} className="space-y-4">
              <div>
                <Label htmlFor="fullName">Full Name</Label>
                <Input id="fullName" placeholder="John Doe" required />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" required />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" type="tel" placeholder="+****************" required />
                </div>
              </div>
              <div>
                <Label>Upload ID (Passport, Driver's License, or ID Card)</Label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                      >
                        <span>Upload a file</span>
                        <input id="file-upload" name="file-upload" type="file" className="sr-only" />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                  </div>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="terms"
                    name="terms"
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    required
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="terms" className="text-gray-700">
                    I agree to the{' '}
                    <a href="/terms" className="text-indigo-600 hover:text-indigo-500">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="/privacy" className="text-indigo-600 hover:text-indigo-500">
                      Privacy Policy
                    </a>
                  </label>
                </div>
              </div>
              <div className="flex justify-end space-x-3 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setVerificationStatus(prev => ({
                    ...prev!,
                    verificationInProgress: false
                  }))}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  Submit for Verification
                </Button>
              </div>
            </form>
          </div>
        )}
      </div>
      )}



      <form id="item-details-form" onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Item Details</CardTitle>
            <CardDescription>Enter information about the item you're selling</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter item title"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your item in detail"
                rows={4}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">Price ($) *</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={formData.price}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  name="quantity"
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                required
              >
                <option value="">Select a category</option>
                <option value="electronics">Electronics</option>
                <option value="fashion">Fashion</option>
                <option value="home">Home & Garden</option>
                <option value="sports">Sports & Outdoors</option>
                <option value="collectibles">Collectibles</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags (comma separated)</Label>
              <Input
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="e.g., vintage, handmade, limited edition"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="shipping">Shipping Options *</Label>
              <select
                id="shipping"
                name="shipping"
                value={formData.shipping}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                required
              >
                <option value="">Select shipping option</option>
                <option value="free">Free Shipping</option>
                <option value="calculated">Calculated Shipping</option>
                <option value="local_pickup">Local Pickup Only</option>
              </select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Photos</CardTitle>
            <CardDescription>Add clear photos of your item</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-6">
              {formData.imagePreview ? (
                <div className="relative w-full max-w-xs mx-auto">
                  <img
                    src={formData.imagePreview}
                    alt="Preview"
                    className="w-full h-48 object-cover rounded-md"
                  />
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, image: null, imagePreview: '' }))}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                    <ImageIcon className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="mt-4 flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer rounded-md bg-white font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2"
                    >
                      <span>Upload a file</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => window.history.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Listing...
              </>
            ) : (
              'List Item for Sale'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SellPage;
