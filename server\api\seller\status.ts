import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user with seller information
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        seller: true,
        sellerVerifications: true
      }
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is a seller
    if (!user.isSeller || !user.seller) {
      return res.json({
        isVerified: false,
        trustScore: 0,
        tier: 'New Seller',
        maxListings: 5,
        platformFee: '5%',
        canListInSafeSphere: false
      });
    }

    // Use the trust score stored in the database (calculated by the rating system)
    // This follows the SELLER_TRUST_SCORE.md specification
    const trustScore = user.trustScore || 30; // Default to 30 if not set

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    return res.json({
      isVerified: !!user.seller, // User has seller account
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    });

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
