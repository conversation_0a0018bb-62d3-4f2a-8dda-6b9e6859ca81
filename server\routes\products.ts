import express from 'express';
import { IStorage } from '../storage';
import multer from 'multer';
import { db } from '../db';
import { categories } from '../../shared/schema';
import { eq } from 'drizzle-orm';

// Configure multer for handling FormData (including file uploads)
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory for processing
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    fieldSize: 10 * 1024 * 1024 // 10MB limit for text fields
  },
  fileFilter: (req, file, cb) => {
    // Accept only images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

function createProductRoutes(storage: IStorage) {
  const router = express.Router();

  // Get all products
  router.get('/', async (req, res) => {
    try {
      const sphere = req.query.sphere as string || 'safesphere';
      const query = req.query.query as string || '';

      const products = await storage.getProducts(sphere, query);
      res.json(products);
    } catch (error) {
      console.error('Error fetching products:', error);
      res.status(500).json({ error: 'Failed to fetch products' });
    }
  });

  // Get a product by ID
  router.get('/:id', async (req, res) => {
    try {
      const id = Number(req.params.id);
      const product = await storage.getProductById(id);

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      res.json(product);
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({ error: 'Failed to fetch product' });
    }
  });

  // Get related products
  router.get('/:id/related', async (req, res) => {
    try {
      const id = Number(req.params.id);
      const product = await storage.getProductById(id);

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      const relatedProducts = await storage.getRelatedProducts(product.tags, id);
      res.json(relatedProducts);
    } catch (error) {
      console.error('Error fetching related products:', error);
      res.status(500).json({ error: 'Failed to fetch related products' });
    }
  });

  // Get products by category
  router.get('/category/:category', async (req, res) => {
    try {
      const category = req.params.category;
      const products = await storage.getProductsByCategory(category);
      res.json(products);
    } catch (error) {
      console.error('Error fetching products by category:', error);
      res.status(500).json({ error: 'Failed to fetch products by category' });
    }
  });

  // Create a new product
  router.post('/', upload.single('image'), async (req, res) => {
    try {
      // Ensure user is authorized to create products
      if (!req.session?.passport?.user) {
        console.log('No user ID in session:', req.session);
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const userId = req.session.passport.user;
      console.log('Creating product for user ID:', userId);
      console.log('Request body received:', JSON.stringify(req.body, null, 2));
      console.log('Request file received:', req.file ? {
        fieldname: req.file.fieldname,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      } : 'No file');
      console.log('Request content-type:', req.headers['content-type']);
      console.log('Request method:', req.method);

      // Get the user to assign seller details
      const user = await storage.getUser(userId);
      if (!user) {
        console.log('User not found in database:', userId);
        return res.status(404).json({ error: 'User not found' });
      }

      // Use the trust score stored in the database (calculated by the rating system)
      // This follows the SELLER_TRUST_SCORE.md specification
      const trustScore = user.trustScore;

      console.log('Using stored trust score for product creation:', {
        userId: user.id,
        trustScore,
        isSeller: user.isSeller,
        identityVerified: user.identityVerified
      });

      // Process image file if uploaded
      let imageUrl = req.body.imageUrl || '';
      if (req.file) {
        // Convert uploaded file to base64 for storage
        // In production, you'd upload to S3/CloudFlare/etc.
        const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;
        imageUrl = base64Image;
        console.log('Processed uploaded image, size:', req.file.size, 'bytes');
      }

      // Process tags - handle both string and array formats
      let tags = req.body.tags || [];
      if (typeof tags === 'string') {
        try {
          // Try to parse as JSON first
          tags = JSON.parse(tags);
        } catch {
          // If not JSON, split by comma
          tags = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
        }
      }
      if (!Array.isArray(tags)) {
        tags = [];
      }

      // Convert price to number (in cents)
      let priceInCents = 0;
      if (req.body.price) {
        const priceValue = typeof req.body.price === 'string' ? parseFloat(req.body.price) : req.body.price;
        priceInCents = Math.round(priceValue * 100); // Convert to cents
      }

      // Convert quantity to number
      const quantity = req.body.quantity ? parseInt(req.body.quantity, 10) : 1;

      // Handle category - convert category ID to proper integer
      let categoryId = null;
      if (req.body.category) {
        try {
          // The frontend now sends category IDs as strings, convert to integer
          const categoryIdNum = parseInt(req.body.category, 10);
          if (!isNaN(categoryIdNum)) {
            // Verify the category exists in the database
            const categoryExists = await db.select({ id: categories.id })
              .from(categories)
              .where(eq(categories.id, categoryIdNum))
              .limit(1);

            if (categoryExists.length > 0) {
              categoryId = categoryIdNum;
              console.log(`✅ Valid category ID: ${categoryId}`);
            } else {
              console.log(`⚠️ Category ID ${categoryIdNum} not found in database`);
            }
          }
        } catch (error) {
          console.error('Error processing category:', error);
        }
      }

      // Create the product data with proper types and field names
      const productData = {
        title: req.body.title || '',
        description: req.body.description || '',
        price: priceInCents,
        imageUrl: imageUrl,
        sellerId: user.id,
        sellerName: user.fullName,
        sellerVerified: user.isSeller,
        sellerType: 'merchant',
        trustScore: trustScore,
        // Copy seller's identity verification status to product (for SafeSphere filtering)
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        tags: tags,
        shipping: req.body.shipping || 'standard',
        originalPrice: null,
        discount: null,
        verifiedSince: null,
        warning: null,
        isBulkBuy: req.body.isBulkBuy === 'true' || req.body.isBulkBuy === true,
        bulkMinimumQuantity: null,
        bulkDiscountRate: null,
        imageDescription: null,
        categoryId: categoryId,
        aiAttributes: {},
        searchVector: '',
        // Product status and inventory management
        status: 'active', // Products go live immediately when created
        quantity: quantity,
        soldQuantity: 0,
        // Note: createdAt and updatedAt will be set automatically by the database
      };

      console.log('Creating product with data:', productData);

      try {
        const product = await storage.createProduct(productData);
        console.log('Product created successfully:', product);

        // Automatically make user a seller when they list their first item
        if (!user.isSeller) {
          console.log(`Making user ${user.id} a seller after listing their first item`);
          try {
            await storage.updateUserSellerStatus(user.id, true);
            console.log(`Successfully updated user ${user.id} to seller status`);
          } catch (sellerUpdateError) {
            console.error('Error updating user seller status:', sellerUpdateError);
            // Don't fail the product creation if seller status update fails
          }
        }

        res.status(201).json(product);
      } catch (dbError) {
        console.error('Database error creating product:', dbError);
        console.error('Product data that failed:', JSON.stringify(productData, null, 2));
        res.status(500).json({
          message: 'Failed to create product in database',
          error: dbError instanceof Error ? dbError.message : 'Unknown database error'
        });
        return;
      }
    } catch (error) {
      console.error('Error creating product:', error);
      res.status(500).json({ error: 'Failed to create product' });
    }
  });

  // Get products by seller ID
  router.get('/seller/:sellerId', async (req, res) => {
    try {
      const sellerId = Number(req.params.sellerId);
      const products = await storage.getProductsBySellerId(sellerId);
      res.json(products);
    } catch (error) {
      console.error('Error fetching products by seller:', error);
      res.status(500).json({ error: 'Failed to fetch products by seller' });
    }
  });

  return router;
}

export { createProductRoutes };