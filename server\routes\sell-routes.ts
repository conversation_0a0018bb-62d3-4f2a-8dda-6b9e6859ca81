import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq, sql } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Submit identity verification
router.post('/api/user/verify-identity', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userId = req.user.id;
    console.log('Processing identity verification for user:', userId);

    // For testing purposes, we'll automatically approve the verification
    // In a real app, you would:
    // 1. Store the uploaded files
    // 2. Process the verification documents
    // 3. Update the user's verification status

    // Prepare verification data
    const verificationData = {
      fullName: req.body.fullName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      address: req.body.address,
      submittedAt: new Date().toISOString(),
      approvedAt: new Date().toISOString()
    };

    console.log('Auto-approving identity verification:', verificationData);

    // Update user in database with identity verification
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user with identity verification (trust score will be recalculated based on current score + 40 bonus)
    const currentUser = await storage.getUser(userId);
    const identityVerificationBonus = 40;
    const oldTrustScore = currentUser.trustScore;
    const newTrustScore = Math.min(100, oldTrustScore + identityVerificationBonus);

    console.log(`🔍 Identity Verification Trust Score Calculation:
      - User ID: ${userId}
      - Current Trust Score: ${oldTrustScore}
      - Identity Verification Bonus: ${identityVerificationBonus}
      - New Trust Score: ${newTrustScore}
      - Already Verified: ${currentUser.identityVerified}`);

    // Check if user is already verified to prevent double bonus
    if (currentUser.identityVerified) {
      console.log(`⚠️ User ${userId} is already identity verified. Skipping bonus.`);
      return res.json({
        message: 'Identity verification already completed!',
        status: 'already_verified',
        trustScoreIncrease: 0,
        newTrustScore: oldTrustScore
      });
    }

    // Update user with identity verification
    await storage.updateUser(userId, {
      identityVerified: true,
      identityVerificationStatus: 'approved',
      identityVerificationSubmittedAt: new Date(),
      identityVerificationApprovedAt: new Date(),
      identityVerificationData: verificationData,
      trustScore: newTrustScore
    });

    console.log(`✅ Updated user ${userId} with identity verification. Trust score: ${oldTrustScore} → ${newTrustScore}`);

    res.json({
      message: 'Identity verification submitted and approved!',
      status: 'approved',
      trustScoreIncrease: 40,
      newTrustScore: newTrustScore
    });

  } catch (error) {
    console.error('Error processing identity verification:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Use the trust score stored in the database (calculated by the rating system)
    // This follows the SELLER_TRUST_SCORE.md specification
    const trustScore = user.trustScore;

    // Get additional info for debugging
    let confirmedSales = 0;
    try {
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      confirmedSales = sellerPurchases.filter(p => p.status === 'received').length;
    } catch (error) {
      console.error('Error getting seller purchases for trust score:', error);
    }

    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus === 'approved';

    // Debug logging
    console.log('🔍 Trust Score Status:', {
      userId: user?.id,
      trustScoreFromDB: user.trustScore,
      confirmedSales,
      hasIdentityVerification,
      identityVerificationStatus: user.identityVerificationStatus
    });

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    const response = {
      isVerified: true, // All logged-in users can list items
      isSeller: user.isSeller, // True if user has listed at least one item
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    };

    console.log('Seller status response:', JSON.stringify(response, null, 2));
    return res.json(response);

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Removed temporary fix-data endpoint to prevent trust score manipulation

// Note: In development mode, the /sell route is handled by Vite's catch-all middleware
// In production mode, it's handled by the serveStatic function in vite.ts

  return router;
}
