import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq, sql } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Submit identity verification
router.post('/api/user/verify-identity', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userId = req.user.id;
    console.log('Processing identity verification submission for user:', userId);

    // Prepare verification data for submission (NOT auto-approved)
    const verificationData = {
      fullName: req.body.fullName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      address: req.body.address,
      submittedAt: new Date().toISOString()
      // Note: approvedAt will be set when admin manually approves
    };

    console.log('Submitting identity verification for review:', verificationData);

    // Get current user to check status
    const currentUser = await storage.getUser(userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`🔍 Identity Verification Submission:
      - User ID: ${userId}
      - Current Status: ${currentUser.identityVerificationStatus}
      - Already Verified: ${currentUser.identityVerified}`);

    // Check if user is already verified
    if (currentUser.identityVerified) {
      console.log(`⚠️ User ${userId} is already identity verified.`);
      return res.json({
        message: 'Identity verification already completed!',
        status: 'already_verified'
      });
    }

    // Check if user already has a pending verification
    if (currentUser.identityVerificationStatus === 'pending') {
      console.log(`⚠️ User ${userId} already has a pending identity verification.`);
      return res.json({
        message: 'Identity verification is already pending review.',
        status: 'pending'
      });
    }

    // Submit verification for review (NOT auto-approved)
    await storage.updateUser(userId, {
      identityVerificationStatus: 'pending',
      identityVerificationSubmittedAt: new Date(),
      identityVerificationData: verificationData
      // Note: identityVerified stays false until admin approval
      // Note: trustScore is NOT updated here - only when approved by admin
    });

    console.log(`✅ Identity verification submitted for user ${userId} - pending admin review`);

    res.json({
      message: 'Identity verification submitted successfully! Your submission is now pending review by our team.',
      status: 'pending'
    });

  } catch (error) {
    console.error('Error processing identity verification:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Use the trust score stored in the database (calculated by the rating system)
    // This follows the SELLER_TRUST_SCORE.md specification
    const trustScore = user.trustScore;

    // Get additional info for debugging
    let confirmedSales = 0;
    try {
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      confirmedSales = sellerPurchases.filter(p => p.status === 'received').length;
    } catch (error) {
      console.error('Error getting seller purchases for trust score:', error);
    }

    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus === 'approved';

    // Debug logging
    console.log('🔍 Trust Score Status:', {
      userId: user?.id,
      trustScoreFromDB: user.trustScore,
      confirmedSales,
      hasIdentityVerification,
      identityVerificationStatus: user.identityVerificationStatus
    });

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    const response = {
      isVerified: true, // All logged-in users can list items
      isSeller: user.isSeller, // True if user has listed at least one item
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    };

    console.log('Seller status response:', JSON.stringify(response, null, 2));
    return res.json(response);

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to approve/reject identity verifications
router.post('/api/admin/identity-verification/:userId/:action', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { userId, action } = req.params;
    const userIdNum = parseInt(userId);

    if (!userIdNum || !['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: 'Invalid user ID or action' });
    }

    // Get the user
    const user = await storage.getUser(userIdNum);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user has a pending verification
    if (user.identityVerificationStatus !== 'pending') {
      return res.status(400).json({
        message: `User does not have a pending verification. Current status: ${user.identityVerificationStatus}`
      });
    }

    if (action === 'approve') {
      // Approve the verification (trigger will automatically add +40 trust score)
      await storage.updateUser(userIdNum, {
        identityVerified: true,
        identityVerificationStatus: 'approved',
        identityVerificationApprovedAt: new Date()
      });

      console.log(`✅ Admin approved identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification approved successfully',
        status: 'approved',
        trustScoreIncrease: 40
      });
    } else {
      // Reject the verification
      await storage.updateUser(userIdNum, {
        identityVerificationStatus: 'rejected'
      });

      console.log(`❌ Admin rejected identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification rejected',
        status: 'rejected'
      });
    }

  } catch (error) {
    console.error('Error processing admin identity verification action:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to list pending identity verifications
router.get('/api/admin/identity-verifications/pending', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    // Get all users with pending verifications
    const pendingUsers = await db.select({
      id: users.id,
      username: users.username,
      email: users.email,
      identityVerificationStatus: users.identityVerificationStatus,
      identityVerificationSubmittedAt: users.identityVerificationSubmittedAt,
      identityVerificationData: users.identityVerificationData
    })
    .from(users)
    .where(eq(users.identityVerificationStatus, 'pending'));

    res.json({
      pendingVerifications: pendingUsers,
      count: pendingUsers.length
    });

  } catch (error) {
    console.error('Error fetching pending identity verifications:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Removed temporary fix-data endpoint to prevent trust score manipulation

// Note: In development mode, the /sell route is handled by Vite's catch-all middleware
// In production mode, it's handled by the serveStatic function in vite.ts

  return router;
}
