import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq, sql } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Submit identity verification
router.post('/api/user/verify-identity', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userId = req.user.id;
    console.log('Processing identity verification submission for user:', userId);

    // Prepare verification data for submission (NOT auto-approved)
    const verificationData = {
      fullName: req.body.fullName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      address: req.body.address,
      submittedAt: new Date().toISOString()
      // Note: approvedAt will be set when admin manually approves
    };

    console.log('Submitting identity verification for review:', verificationData);

    // Get current user to check status
    const currentUser = await storage.getUser(userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`🔍 Identity Verification Submission:
      - User ID: ${userId}
      - Current Status: ${currentUser.identityVerificationStatus}
      - Already Verified: ${currentUser.identityVerified}`);

    // Check if user is already verified
    if (currentUser.identityVerified) {
      console.log(`⚠️ User ${userId} is already identity verified.`);
      return res.json({
        message: 'Identity verification already completed!',
        status: 'already_verified'
      });
    }

    // Check if user already has a pending verification
    if (currentUser.identityVerificationStatus === 'app_pending') {
      console.log(`⚠️ User ${userId} already has a pending identity verification.`);
      return res.json({
        message: 'Identity verification is already pending review.',
        status: 'pending'
      });
    }

    // Submit verification for review (NOT auto-approved)
    await storage.updateUser(userId, {
      identityVerificationStatus: 'app_pending', // Use 'app_pending' so it's not sanitized to 'none'
      identityVerificationSubmittedAt: new Date(),
      identityVerificationData: verificationData
      // Note: identityVerified stays false until admin approval
      // Note: trustScore is NOT updated here - only when approved by admin
    });

    console.log(`✅ Identity verification submitted for user ${userId} - pending admin review`);

    res.json({
      message: 'Identity verification submitted successfully! Your submission is now pending review by our team.',
      status: 'pending'
    });

  } catch (error) {
    console.error('Error processing identity verification:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Use the trust score stored in the database (calculated by the rating system)
    // This follows the SELLER_TRUST_SCORE.md specification
    const trustScore = user.trustScore;

    // Get additional info for debugging
    let confirmedSales = 0;
    try {
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      confirmedSales = sellerPurchases.filter(p => p.status === 'received').length;
    } catch (error) {
      console.error('Error getting seller purchases for trust score:', error);
    }

    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

    // Debug logging
    console.log('🔍 Trust Score Status:', {
      userId: user?.id,
      trustScoreFromDB: user.trustScore,
      confirmedSales,
      hasIdentityVerification,
      identityVerificationStatus: user.identityVerificationStatus,
      identityVerified: user.identityVerified,
      identityVerificationApprovedAt: user.identityVerificationApprovedAt
    });

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70 && hasIdentityVerification) {
      // Users with 70+ points AND identity verification get "Trusted" tier
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (hasIdentityVerification) {
      // Users with identity verification but less than 70 points get "Verified" tier
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
      canListInSafeSphere = true;
    } else if (trustScore > 30) {
      // Users with some sales but no identity verification
      tier = 'Basic Seller';
      maxListings = 10;
      platformFee = '4%';
    }
    // Users with exactly 30 points (base score) and no verification remain "New Seller"

    const response = {
      isVerified: true, // All logged-in users can list items
      isSeller: user.isSeller, // True if user has listed at least one item
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    };

    console.log('Seller status response:', JSON.stringify(response, null, 2));
    return res.json(response);

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to approve/reject identity verifications
router.post('/api/admin/identity-verification/:userId/:action', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { userId, action } = req.params;
    const userIdNum = parseInt(userId);

    if (!userIdNum || !['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: 'Invalid user ID or action' });
    }

    // Get the user
    const user = await storage.getUser(userIdNum);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user has a pending verification
    if (user.identityVerificationStatus !== 'app_pending') {
      return res.status(400).json({
        message: `User does not have a pending verification. Current status: ${user.identityVerificationStatus}`
      });
    }

    if (action === 'approve') {
      // Get user before update for comparison
      const userBefore = await storage.getUser(userIdNum);
      console.log('🔍 User BEFORE approval:', {
        id: userBefore?.id,
        identityVerified: userBefore?.identityVerified,
        identityVerificationStatus: userBefore?.identityVerificationStatus,
        trustScore: userBefore?.trustScore
      });

      // Approve the verification (trigger will automatically add +40 trust score)
      const updatedUser = await storage.updateUser(userIdNum, {
        identityVerified: true,
        identityVerificationStatus: 'approved',
        identityVerificationApprovedAt: new Date()
      });

      console.log('🔍 User AFTER approval:', {
        id: updatedUser?.id,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus,
        trustScore: updatedUser?.trustScore
      });

      console.log(`✅ Admin approved identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification approved successfully',
        status: 'approved',
        trustScoreIncrease: 40,
        updatedUser: updatedUser
      });
    } else {
      // Reject the verification - reset to none (database will sanitize 'rejected' to 'none' anyway)
      await storage.updateUser(userIdNum, {
        identityVerified: false,
        identityVerificationStatus: 'none'
      });

      console.log(`❌ Admin rejected identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification rejected',
        status: 'rejected' // For API response, but database will store as 'none'
      });
    }

  } catch (error) {
    console.error('Error processing admin identity verification action:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to list pending identity verifications
router.get('/api/admin/identity-verifications/pending', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    // Get all users with pending verifications
    const pendingUsers = await db.select({
      id: users.id,
      username: users.username,
      email: users.email,
      identityVerificationStatus: users.identityVerificationStatus,
      identityVerificationSubmittedAt: users.identityVerificationSubmittedAt,
      identityVerificationData: users.identityVerificationData
    })
    .from(users)
    .where(eq(users.identityVerificationStatus, 'app_pending'));

    res.json({
      pendingVerifications: pendingUsers,
      count: pendingUsers.length
    });

  } catch (error) {
    console.error('Error fetching pending identity verifications:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Debug endpoint to check user data directly
router.get('/api/debug/user/:userId', isAuthenticated, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    if (!userId) {
      return res.status(400).json({ message: 'Invalid user ID' });
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      id: user.id,
      username: user.username,
      trustScore: user.trustScore,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus,
      identityVerificationApprovedAt: user.identityVerificationApprovedAt,
      identityVerificationSubmittedAt: user.identityVerificationSubmittedAt
    });

  } catch (error) {
    console.error('Error fetching user debug data:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Debug endpoint to manually fix user 3's trust score
router.get('/api/debug/fix-user3-trust-score', async (req, res) => {
  try {
    // Get user 3
    const user = await storage.getUser(3);
    if (!user) {
      return res.status(404).json({ message: 'User 3 not found' });
    }

    console.log('🔍 User 3 BEFORE fix:', {
      id: user.id,
      trustScore: user.trustScore,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus
    });

    // Check if user should have +40 trust score
    const shouldHaveBonus = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

    if (shouldHaveBonus && user.trustScore === 30) {
      // Update trust score to 70
      await storage.updateUser(3, { trustScore: 70 });

      const updatedUser = await storage.getUser(3);
      console.log('🔍 User 3 AFTER fix:', {
        id: updatedUser?.id,
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      });

      res.json({
        message: 'User 3 trust score fixed',
        before: { trustScore: 30 },
        after: { trustScore: updatedUser?.trustScore },
        reason: 'Identity verified and approved'
      });
    } else {
      res.json({
        message: 'No fix needed',
        currentTrustScore: user.trustScore,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        shouldHaveBonus
      });
    }

  } catch (error) {
    console.error('Error fixing user 3 trust score:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Debug endpoint to test "waiting for approval" state
router.get('/api/debug/set-user3-waiting-approval', async (req, res) => {
  try {
    // Set user 3 to have identityVerified=true but identityVerificationStatus='waiting_approval'
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'waiting_approval',
      trustScore: 30 // Keep at 30 to show no bonus yet
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 set to waiting approval state:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 set to waiting approval state',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error setting user 3 to waiting approval:', error);
    res.status(500).json({ error: 'Failed to set waiting approval state' });
  }
});

// Debug endpoint to get seller status for user 3 (to test UI logic)
router.get('/api/debug/user3-seller-status', async (req, res) => {
  try {
    const user = await storage.getUser(3);
    if (!user) {
      return res.status(404).json({ error: 'User 3 not found' });
    }

    // Calculate seller status like the real endpoint does
    const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
    const confirmedSales = sellerPurchases.filter(p => p.status === 'received');
    const trustScore = user.trustScore;
    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70 && hasIdentityVerification) {
      // Users with 70+ points AND identity verification get "Trusted" tier
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (hasIdentityVerification) {
      // Users with identity verification but less than 70 points get "Verified" tier
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
      canListInSafeSphere = true;
    } else if (trustScore > 30) {
      // Users with some sales but no identity verification
      tier = 'Basic Seller';
      maxListings = 10;
      platformFee = '4%';
    }
    // Users with exactly 30 points (base score) and no verification remain "New Seller"

    const response = {
      isVerified: true,
      isSeller: user.isSeller,
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere,
      // Additional debug info
      user: {
        id: user.id,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        trustScore: user.trustScore
      },
      uiState: {
        shouldShowWaitingForApproval: user.identityVerified && !user.identityVerificationStatus?.startsWith('app'),
        shouldShowStartVerification: !user.identityVerified || user.identityVerificationStatus === 'none',
        shouldShowCompleted: hasIdentityVerification
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting user 3 seller status:', error);
    res.status(500).json({ error: 'Failed to get seller status' });
  }
});

// Debug endpoint to approve user 3's identity verification
router.get('/api/debug/approve-user3', async (req, res) => {
  try {
    // Approve user 3's verification (this should trigger the +40 trust score)
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'approved',
      identityVerificationApprovedAt: new Date()
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 approved:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 identity verification approved',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error approving user 3:', error);
    res.status(500).json({ error: 'Failed to approve user 3' });
  }
});

// Debug endpoint to reset user 3 to not started verification state
router.get('/api/debug/reset-user3-verification', async (req, res) => {
  try {
    // Reset user 3 to have identityVerified=false and no status
    await storage.updateUser(3, {
      identityVerified: false,
      identityVerificationStatus: 'none',
      trustScore: 30 // Keep at 30 (no bonus)
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 reset to not started state:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 reset to not started verification state',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error resetting user 3 verification:', error);
    res.status(500).json({ error: 'Failed to reset verification state' });
  }
});

// Debug endpoint to test sanitization with invalid status
router.get('/api/debug/test-sanitization', async (req, res) => {
  try {
    // Try to set user 3's status to an invalid value
    await storage.updateUser(3, {
      identityVerified: false,
      identityVerificationStatus: 'fdjfksjfds', // This should become 'none'
      trustScore: 30
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 after sanitization test:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'Sanitization test completed',
      attempted: 'fdjfksjfds',
      actual: updatedUser?.identityVerificationStatus,
      sanitized: updatedUser?.identityVerificationStatus === 'none'
    });
  } catch (error) {
    console.error('Error testing sanitization:', error);
    res.status(500).json({ error: 'Failed to test sanitization' });
  }
});

// Debug endpoint to test the new sanitization logic (only 'app*' values allowed)
router.get('/api/debug/test-new-sanitization', async (req, res) => {
  try {
    const tests = [
      { value: 'approved', shouldBe: 'approved' }, // starts with 'app'
      { value: 'app_waiting', shouldBe: 'app_waiting' }, // starts with 'app'
      { value: 'application_review', shouldBe: 'application_review' }, // starts with 'app'
      { value: 'pending', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
      { value: 'rejected', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
      { value: 'random_text', shouldBe: 'none' }, // should be sanitized
      { value: 'fdjfksjfds', shouldBe: 'none' }, // should be sanitized
      { value: '', shouldBe: 'none' }, // empty string should be sanitized
      { value: 'none', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
    ];

    const results = [];

    for (const test of tests) {
      await storage.updateUser(3, {
        identityVerificationStatus: test.value,
        trustScore: 30
      });

      const user = await storage.getUser(3);
      const result = {
        attempted: test.value,
        expected: test.shouldBe,
        actual: user?.identityVerificationStatus,
        correct: user?.identityVerificationStatus === test.shouldBe
      };
      results.push(result);
    }

    res.json({
      message: 'Validation tests completed',
      results,
      allPassed: results.every(r => r.correct)
    });
  } catch (error) {
    console.error('Error testing valid values:', error);
    res.status(500).json({ error: 'Failed to test valid values' });
  }
});

// Debug endpoint to test setting status to just 'app'
router.get('/api/debug/test-app-status', async (req, res) => {
  try {
    // Set user 3's status to just 'app' and identityVerified to true
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'app',
      // Don't set trustScore - let the trigger handle it
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 after setting status to "app":', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'Set status to "app"',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      },
      shouldHaveBonus: updatedUser?.identityVerified && updatedUser?.identityVerificationStatus?.startsWith('app')
    });
  } catch (error) {
    console.error('Error testing app status:', error);
    res.status(500).json({ error: 'Failed to test app status' });
  }
});

// Debug endpoint to test various 'app*' values
router.get('/api/debug/test-app-variations', async (req, res) => {
  try {
    const testValues = ['app', 'approved', 'app_pending', 'application_review', 'app123'];
    const results = [];

    for (const value of testValues) {
      // Set the status
      await storage.updateUser(3, {
        identityVerified: true,
        identityVerificationStatus: value,
      });

      // Get the updated user
      const user = await storage.getUser(3);

      // Check seller status
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      const confirmedSales = sellerPurchases.filter(p => p.status === 'received');
      const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

      results.push({
        testedValue: value,
        actualValue: user.identityVerificationStatus,
        trustScore: user.trustScore,
        hasIdentityVerification,
        shouldShowCompleted: hasIdentityVerification
      });
    }

    res.json({
      message: 'Tested various app* values',
      results,
      allWorking: results.every(r => r.hasIdentityVerification && r.trustScore === 70)
    });
  } catch (error) {
    console.error('Error testing app variations:', error);
    res.status(500).json({ error: 'Failed to test app variations' });
  }
});

// Debug endpoint to test user creation with default values
router.get('/api/debug/test-user-creation', async (req, res) => {
  try {
    // Create a test user with minimal data (should use schema defaults)
    const testUser = await storage.createUser({
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'hashedpassword123'
    });

    console.log('🔍 Newly created test user:', {
      id: testUser.id,
      username: testUser.username,
      trustScore: testUser.trustScore,
      identityVerified: testUser.identityVerified,
      identityVerificationStatus: testUser.identityVerificationStatus,
      dasWosCoinsBalance: testUser.dasWosCoinsBalance
    });

    res.json({
      message: 'Test user created',
      user: {
        id: testUser.id,
        username: testUser.username,
        trustScore: testUser.trustScore,
        identityVerified: testUser.identityVerified,
        identityVerificationStatus: testUser.identityVerificationStatus,
        dasWosCoinsBalance: testUser.dasWosCoinsBalance
      },
      expectedDefaults: {
        trustScore: 30,
        identityVerified: false,
        identityVerificationStatus: 'none',
        dasWosCoinsBalance: 0
      }
    });
  } catch (error) {
    console.error('Error testing user creation:', error);
    res.status(500).json({ error: 'Failed to test user creation' });
  }
});

// Debug endpoint to check user test2
router.get('/api/debug/check-test2', async (req, res) => {
  try {
    const user = await storage.getUserByUsername('test2');

    if (!user) {
      return res.json({ message: 'User test2 not found' });
    }

    console.log('🔍 User test2 details:', {
      id: user.id,
      username: user.username,
      trustScore: user.trustScore,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus,
      dasWosCoinsBalance: user.dasWosCoinsBalance,
      createdAt: user.createdAt
    });

    res.json({
      message: 'User test2 found',
      user: {
        id: user.id,
        username: user.username,
        trustScore: user.trustScore,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        dasWosCoinsBalance: user.dasWosCoinsBalance,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Error checking test2:', error);
    res.status(500).json({ error: 'Failed to check test2' });
  }
});

// Debug endpoint to check for duplicate usernames
router.get('/api/debug/check-duplicates/:username', async (req, res) => {
  try {
    const username = req.params.username;

    // Get all users with this username (should only be one due to unique constraint)
    const allUsers = await db.select().from(users).where(sql`LOWER(${users.username}) = LOWER(${username})`);

    console.log(`🔍 Found ${allUsers.length} users with username "${username}":`, allUsers.map(u => ({
      id: u.id,
      username: u.username,
      identityVerified: u.identityVerified,
      identityVerificationStatus: u.identityVerificationStatus,
      trustScore: u.trustScore,
      createdAt: u.createdAt
    })));

    res.json({
      message: `Found ${allUsers.length} users with username "${username}"`,
      users: allUsers.map(u => ({
        id: u.id,
        username: u.username,
        identityVerified: u.identityVerified,
        identityVerificationStatus: u.identityVerificationStatus,
        trustScore: u.trustScore,
        createdAt: u.createdAt
      }))
    });
  } catch (error) {
    console.error('Error checking duplicates:', error);
    res.status(500).json({ error: 'Failed to check duplicates' });
  }
});

// Admin endpoint to reset a user's verification data to defaults
router.get('/api/admin/reset-user-verification/:userId', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const userId = parseInt(req.params.userId);
    if (!userId) {
      return res.status(400).json({ message: 'Invalid user ID' });
    }

    // Get the user first
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`🔄 Admin resetting verification data for user ${userId} (${user.username})`);
    console.log('Before reset:', {
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus,
      trustScore: user.trustScore
    });

    // Reset to default values
    const updatedUser = await storage.updateUser(userId, {
      identityVerified: false,
      identityVerificationStatus: 'none',
      trustScore: 30, // Base score
      identityVerificationSubmittedAt: null,
      identityVerificationApprovedAt: null,
      identityVerificationData: {}
    });

    console.log('After reset:', {
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus,
      trustScore: updatedUser?.trustScore
    });

    res.json({
      message: `User ${user.username} verification data reset to defaults`,
      before: {
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        trustScore: user.trustScore
      },
      after: {
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus,
        trustScore: updatedUser?.trustScore
      }
    });
  } catch (error) {
    console.error('Error resetting user verification:', error);
    res.status(500).json({ error: 'Failed to reset user verification' });
  }
});

// Debug endpoint to trace user creation step by step
router.get('/api/debug/trace-user-creation', async (req, res) => {
  try {
    const timestamp = Date.now();
    const username = `trace_${timestamp}`;
    const email = `trace_${timestamp}@example.com`;

    console.log('🔍 STEP 1: About to create user with minimal data');
    console.log('Input data:', {
      username,
      email,
      fullName: 'Trace User',
      password: 'hashedpassword123'
    });

    // Create user and immediately check what was inserted
    const newUser = await storage.createUser({
      username,
      email,
      fullName: 'Trace User',
      password: 'hashedpassword123'
    });

    console.log('🔍 STEP 2: User created, checking returned object');
    console.log('Returned user object:', {
      id: newUser.id,
      username: newUser.username,
      identityVerified: newUser.identityVerified,
      identityVerificationStatus: newUser.identityVerificationStatus,
      trustScore: newUser.trustScore,
      dasWosCoinsBalance: newUser.dasWosCoinsBalance
    });

    // Now fetch the user directly from database to see what's actually stored
    console.log('🔍 STEP 3: Fetching user directly from database');
    const fetchedUser = await storage.getUser(newUser.id);

    console.log('Database stored values:', {
      id: fetchedUser?.id,
      username: fetchedUser?.username,
      identityVerified: fetchedUser?.identityVerified,
      identityVerificationStatus: fetchedUser?.identityVerificationStatus,
      trustScore: fetchedUser?.trustScore,
      dasWosCoinsBalance: fetchedUser?.dasWosCoinsBalance
    });

    // Check if there's a discrepancy
    const hasDiscrepancy =
      newUser.identityVerified !== false ||
      newUser.identityVerificationStatus !== 'none' ||
      newUser.trustScore !== 30 ||
      newUser.dasWosCoinsBalance !== 0;

    res.json({
      message: 'User creation traced',
      input: {
        username,
        email,
        fullName: 'Trace User'
      },
      returnedFromCreate: {
        id: newUser.id,
        identityVerified: newUser.identityVerified,
        identityVerificationStatus: newUser.identityVerificationStatus,
        trustScore: newUser.trustScore,
        dasWosCoinsBalance: newUser.dasWosCoinsBalance
      },
      fetchedFromDatabase: {
        id: fetchedUser?.id,
        identityVerified: fetchedUser?.identityVerified,
        identityVerificationStatus: fetchedUser?.identityVerificationStatus,
        trustScore: fetchedUser?.trustScore,
        dasWosCoinsBalance: fetchedUser?.dasWosCoinsBalance
      },
      expectedDefaults: {
        identityVerified: false,
        identityVerificationStatus: 'none',
        trustScore: 30,
        dasWosCoinsBalance: 0
      },
      hasDiscrepancy,
      discrepancyDetails: hasDiscrepancy ? {
        identityVerified: `Expected: false, Got: ${newUser.identityVerified}`,
        identityVerificationStatus: `Expected: 'none', Got: '${newUser.identityVerificationStatus}'`,
        trustScore: `Expected: 30, Got: ${newUser.trustScore}`,
        dasWosCoinsBalance: `Expected: 0, Got: ${newUser.dasWosCoinsBalance}`
      } : null
    });
  } catch (error) {
    console.error('Error tracing user creation:', error);
    res.status(500).json({ error: 'Failed to trace user creation', details: error.message });
  }
});

// Debug endpoint to test user creation with explicit null/undefined values
router.get('/api/debug/test-explicit-values', async (req, res) => {
  try {
    const timestamp = Date.now();

    // Test 1: Create user with explicit null values
    console.log('🔍 TEST 1: Creating user with explicit null values');
    const user1 = await storage.createUser({
      username: `test_null_${timestamp}`,
      email: `test_null_${timestamp}@example.com`,
      fullName: 'Test Null User',
      password: 'hashedpassword123',
      identityVerified: null as any,
      identityVerificationStatus: null as any
    });

    // Test 2: Create user with explicit undefined values
    console.log('🔍 TEST 2: Creating user with explicit undefined values');
    const user2 = await storage.createUser({
      username: `test_undefined_${timestamp}`,
      email: `test_undefined_${timestamp}@example.com`,
      fullName: 'Test Undefined User',
      password: 'hashedpassword123',
      identityVerified: undefined as any,
      identityVerificationStatus: undefined as any
    });

    // Test 3: Create user with explicit false/none values
    console.log('🔍 TEST 3: Creating user with explicit false/none values');
    const user3 = await storage.createUser({
      username: `test_explicit_${timestamp}`,
      email: `test_explicit_${timestamp}@example.com`,
      fullName: 'Test Explicit User',
      password: 'hashedpassword123',
      identityVerified: false,
      identityVerificationStatus: 'none'
    });

    res.json({
      message: 'Tested user creation with explicit values',
      results: {
        nullValues: {
          id: user1.id,
          identityVerified: user1.identityVerified,
          identityVerificationStatus: user1.identityVerificationStatus,
          trustScore: user1.trustScore
        },
        undefinedValues: {
          id: user2.id,
          identityVerified: user2.identityVerified,
          identityVerificationStatus: user2.identityVerificationStatus,
          trustScore: user2.trustScore
        },
        explicitValues: {
          id: user3.id,
          identityVerified: user3.identityVerified,
          identityVerificationStatus: user3.identityVerificationStatus,
          trustScore: user3.trustScore
        }
      }
    });
  } catch (error) {
    console.error('Error testing explicit values:', error);
    res.status(500).json({ error: 'Failed to test explicit values', details: error.message });
  }
});

// Debug endpoint to check all tables and their data
router.get('/api/debug/check-all-tables', async (req, res) => {
  try {
    console.log('🔍 Checking all tables in the database...');

    // Get list of all tables
    const tablesResult = await db.execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    const tables = tablesResult.map((row: any) => row.table_name);
    console.log('📋 Found tables:', tables);

    const tableData: any = {};

    // Check each table for data
    for (const tableName of tables) {
      try {
        // Get row count
        const countResult = await db.execute(sql.raw(`SELECT COUNT(*) as count FROM "${tableName}"`));
        const rowCount = countResult[0]?.count || 0;

        // Get sample data if table has rows
        let sampleData = [];
        if (rowCount > 0) {
          const sampleResult = await db.execute(sql.raw(`SELECT * FROM "${tableName}" LIMIT 5`));
          sampleData = sampleResult;
        }

        tableData[tableName] = {
          rowCount: parseInt(rowCount),
          sampleData: sampleData
        };

        console.log(`📊 Table "${tableName}": ${rowCount} rows`);
      } catch (error) {
        console.error(`❌ Error checking table "${tableName}":`, error);
        tableData[tableName] = {
          error: error.message
        };
      }
    }

    // Specifically check for user-related data
    const userRelatedTables = tables.filter(table =>
      table.includes('user') ||
      table.includes('session') ||
      table === 'users'
    );

    res.json({
      message: 'Database tables checked',
      totalTables: tables.length,
      allTables: tables,
      userRelatedTables,
      tableData,
      potentialDataSources: userRelatedTables.filter(table =>
        tableData[table] && tableData[table].rowCount > 0
      )
    });
  } catch (error) {
    console.error('Error checking tables:', error);
    res.status(500).json({ error: 'Failed to check tables', details: error.message });
  }
});

// Debug endpoint to test tier logic with different scenarios
router.get('/api/debug/test-tier-logic', async (req, res) => {
  try {
    const scenarios = [
      { trustScore: 30, identityVerified: false, identityVerificationStatus: 'none', description: 'New user (base score, no verification)' },
      { trustScore: 35, identityVerified: false, identityVerificationStatus: 'none', description: 'User with some sales, no verification' },
      { trustScore: 70, identityVerified: true, identityVerificationStatus: 'approved', description: 'User with identity verification (70 points)' },
      { trustScore: 75, identityVerified: true, identityVerificationStatus: 'approved', description: 'User with verification + sales (75 points)' },
      { trustScore: 85, identityVerified: true, identityVerificationStatus: 'approved', description: 'Premium user (85+ points)' },
      { trustScore: 75, identityVerified: false, identityVerificationStatus: 'none', description: 'User with sales but no verification' }
    ];

    const results = scenarios.map(scenario => {
      const { trustScore, identityVerified, identityVerificationStatus } = scenario;
      const hasIdentityVerification = identityVerified && identityVerificationStatus?.startsWith('app');

      let tier = 'New Seller';
      let maxListings = 5;
      let platformFee = '5%';
      let canListInSafeSphere = false;

      if (trustScore >= 85) {
        tier = 'Premium';
        maxListings = 200;
        platformFee = '1.5%';
        canListInSafeSphere = true;
      } else if (trustScore >= 70 && hasIdentityVerification) {
        tier = 'Trusted';
        maxListings = 50;
        platformFee = '2.5%';
        canListInSafeSphere = true;
      } else if (hasIdentityVerification) {
        tier = 'Verified';
        maxListings = 20;
        platformFee = '3.5%';
        canListInSafeSphere = true;
      } else if (trustScore > 30) {
        tier = 'Basic Seller';
        maxListings = 10;
        platformFee = '4%';
      }

      return {
        ...scenario,
        result: {
          tier,
          maxListings,
          platformFee,
          canListInSafeSphere,
          hasIdentityVerification
        }
      };
    });

    res.json({
      message: 'Tier logic test results',
      scenarios: results
    });
  } catch (error) {
    console.error('Error testing tier logic:', error);
    res.status(500).json({ error: 'Failed to test tier logic' });
  }
});

// Debug endpoint to check user 2's status and their listings
router.get('/api/debug/check-user2-listings', async (req, res) => {
  try {
    // Get user 2
    const user = await storage.getUser(2);
    if (!user) {
      return res.status(404).json({ message: 'User 2 not found' });
    }

    console.log('🔍 User 2 details:', {
      id: user.id,
      username: user.username,
      trustScore: user.trustScore,
      isSeller: user.isSeller,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus
    });

    // Get user 2's listings
    const allProducts = await storage.getProducts('opensphere');
    const user2Products = allProducts.filter(p => p.sellerId === 2);

    console.log('🔍 User 2 listings:', user2Products.map(p => ({
      id: p.id,
      title: p.title,
      trustScore: p.trustScore,
      sellerVerified: p.sellerVerified,
      sellerId: p.sellerId
    })));

    // Check if these products would appear in SafeSphere
    const safeSphereProducts = await storage.getProducts('safesphere');
    const user2InSafeSphere = safeSphereProducts.filter(p => p.sellerId === 2);

    console.log('🔍 User 2 products in SafeSphere:', user2InSafeSphere.length);

    // Check what the SafeSphere criteria should be
    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');
    const shouldBeInSafeSphere = user.trustScore >= 70 && user.isSeller && hasIdentityVerification;

    res.json({
      message: 'User 2 listing analysis',
      user: {
        id: user.id,
        username: user.username,
        trustScore: user.trustScore,
        isSeller: user.isSeller,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        hasIdentityVerification
      },
      listings: {
        totalListings: user2Products.length,
        listingsInOpenSphere: user2Products.length,
        listingsInSafeSphere: user2InSafeSphere.length,
        shouldBeInSafeSphere,
        safeSphereRequirements: {
          trustScore: user.trustScore >= 70,
          isSeller: user.isSeller,
          hasIdentityVerification
        }
      },
      productDetails: user2Products.map(p => ({
        id: p.id,
        title: p.title,
        trustScore: p.trustScore,
        sellerVerified: p.sellerVerified,
        sellerId: p.sellerId
      }))
    });
  } catch (error) {
    console.error('Error checking user 2 listings:', error);
    res.status(500).json({ error: 'Failed to check user 2 listings', details: error.message });
  }
});

// Debug endpoint to test SafeSphere requirements
router.get('/api/debug/test-safesphere-requirements', async (req, res) => {
  try {
    // Test different scenarios for SafeSphere inclusion
    const scenarios = [
      {
        description: 'User 2: Trust score 70, no identity verification',
        trustScore: 70,
        sellerVerified: true,
        identityVerified: false,
        identityVerificationStatus: 'none'
      },
      {
        description: 'Hypothetical: Trust score 70, with identity verification',
        trustScore: 70,
        sellerVerified: true,
        identityVerified: true,
        identityVerificationStatus: 'approved'
      },
      {
        description: 'Hypothetical: Trust score 50, with identity verification',
        trustScore: 50,
        sellerVerified: true,
        identityVerified: true,
        identityVerificationStatus: 'approved'
      },
      {
        description: 'Hypothetical: Trust score 85, no identity verification',
        trustScore: 85,
        sellerVerified: true,
        identityVerified: false,
        identityVerificationStatus: 'none'
      }
    ];

    const results = scenarios.map(scenario => {
      const { trustScore, sellerVerified, identityVerified, identityVerificationStatus } = scenario;

      // Current SafeSphere logic: trustScore >= 70 AND sellerVerified = true
      const currentLogicQualifies = trustScore >= 70 && sellerVerified;

      // Proposed SafeSphere logic: trustScore >= 70 AND sellerVerified = true AND identity verification
      const hasIdentityVerification = identityVerified && identityVerificationStatus?.startsWith('app');
      const proposedLogicQualifies = trustScore >= 70 && sellerVerified && hasIdentityVerification;

      // Alternative logic: Just trust score and seller status (no identity requirement)
      const alternativeLogicQualifies = trustScore >= 70 && sellerVerified;

      return {
        ...scenario,
        results: {
          currentLogic: currentLogicQualifies,
          proposedLogic: proposedLogicQualifies,
          alternativeLogic: alternativeLogicQualifies,
          hasIdentityVerification
        }
      };
    });

    res.json({
      message: 'SafeSphere requirements analysis',
      currentRequirements: {
        trustScore: '>=70',
        sellerVerified: 'true',
        identityVerification: 'not required'
      },
      proposedRequirements: {
        trustScore: '>=70',
        sellerVerified: 'true',
        identityVerification: 'required'
      },
      scenarios: results,
      recommendation: 'User 2 qualifies under current logic but not under proposed stricter logic'
    });
  } catch (error) {
    console.error('Error testing SafeSphere requirements:', error);
    res.status(500).json({ error: 'Failed to test SafeSphere requirements' });
  }
});

// Debug endpoint to test actual SafeSphere filtering
router.get('/api/debug/test-actual-safesphere', async (req, res) => {
  try {
    // Get all products from OpenSphere
    const openSphereProducts = await storage.getProducts('opensphere');
    console.log('🔍 OpenSphere products:', openSphereProducts.length);

    // Get all products from SafeSphere
    const safeSphereProducts = await storage.getProducts('safesphere');
    console.log('🔍 SafeSphere products:', safeSphereProducts.length);

    // Find User 2's products specifically
    const user2OpenSphere = openSphereProducts.filter(p => p.sellerId === 2);
    const user2SafeSphere = safeSphereProducts.filter(p => p.sellerId === 2);

    console.log('🔍 User 2 in OpenSphere:', user2OpenSphere.length);
    console.log('🔍 User 2 in SafeSphere:', user2SafeSphere.length);

    // Analyze why User 2's products might not be in SafeSphere
    const user2Analysis = user2OpenSphere.map(product => {
      const meetsRequirements = {
        trustScore: product.trustScore >= 70,
        sellerVerified: product.sellerVerified === true,
        overall: product.trustScore >= 70 && product.sellerVerified === true
      };

      return {
        productId: product.id,
        title: product.title,
        sellerId: product.sellerId,
        trustScore: product.trustScore,
        sellerVerified: product.sellerVerified,
        meetsRequirements,
        inSafeSphere: user2SafeSphere.some(sp => sp.id === product.id)
      };
    });

    // Get a sample of other products for comparison
    const otherProducts = openSphereProducts.filter(p => p.sellerId !== 2).slice(0, 5);
    const otherAnalysis = otherProducts.map(product => ({
      productId: product.id,
      title: product.title,
      sellerId: product.sellerId,
      trustScore: product.trustScore,
      sellerVerified: product.sellerVerified,
      meetsRequirements: product.trustScore >= 70 && product.sellerVerified === true,
      inSafeSphere: safeSphereProducts.some(sp => sp.id === product.id)
    }));

    res.json({
      message: 'Actual SafeSphere filtering test',
      summary: {
        totalOpenSphere: openSphereProducts.length,
        totalSafeSphere: safeSphereProducts.length,
        user2InOpenSphere: user2OpenSphere.length,
        user2InSafeSphere: user2SafeSphere.length
      },
      user2Analysis,
      otherProductsSample: otherAnalysis,
      safeSphereRequirements: {
        trustScore: '>=70',
        sellerVerified: 'true'
      }
    });
  } catch (error) {
    console.error('Error testing actual SafeSphere:', error);
    res.status(500).json({ error: 'Failed to test actual SafeSphere', details: error.message });
  }
});

// Debug endpoint to fix User 2's product SafeSphere eligibility
router.get('/api/debug/fix-user2-safesphere', async (req, res) => {
  try {
    // Get User 2
    const user = await storage.getUser(2);
    if (!user) {
      return res.status(404).json({ message: 'User 2 not found' });
    }

    // Get User 2's products
    const user2Products = await storage.getProductsBySellerId(2);

    if (user2Products.length === 0) {
      return res.status(404).json({ message: 'User 2 has no products' });
    }

    console.log('🔧 Fixing User 2 products for SafeSphere eligibility...');
    console.log('User 2 current status:', {
      trustScore: user.trustScore,
      isSeller: user.isSeller,
      identityVerified: user.identityVerified
    });

    const results = [];

    // Update each product to have correct values
    for (const product of user2Products) {
      console.log(`🔧 Updating product ${product.id}: ${product.title}`);
      console.log('Before update:', {
        trustScore: product.trustScore,
        sellerVerified: product.sellerVerified,
        sellerId: product.sellerId
      });

      // Update the product with current user values
      const updatedProduct = await db
        .update(products)
        .set({
          trustScore: user.trustScore, // Use current user trust score
          sellerVerified: user.isSeller, // Use current user seller status
          updatedAt: new Date()
        })
        .where(eq(products.id, product.id))
        .returning();

      console.log('After update:', {
        trustScore: updatedProduct[0]?.trustScore,
        sellerVerified: updatedProduct[0]?.sellerVerified,
        sellerId: updatedProduct[0]?.sellerId
      });

      results.push({
        productId: product.id,
        title: product.title,
        before: {
          trustScore: product.trustScore,
          sellerVerified: product.sellerVerified
        },
        after: {
          trustScore: updatedProduct[0]?.trustScore,
          sellerVerified: updatedProduct[0]?.sellerVerified
        },
        shouldBeInSafeSphere: updatedProduct[0]?.trustScore >= 70 && updatedProduct[0]?.sellerVerified
      });
    }

    res.json({
      message: 'User 2 products updated for SafeSphere eligibility',
      userStatus: {
        trustScore: user.trustScore,
        isSeller: user.isSeller,
        identityVerified: user.identityVerified
      },
      updatedProducts: results
    });
  } catch (error) {
    console.error('Error fixing User 2 SafeSphere eligibility:', error);
    res.status(500).json({ error: 'Failed to fix User 2 SafeSphere eligibility', details: error.message });
  }
});

// Admin endpoint to completely reset all user-related data
router.get('/api/admin/complete-reset', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    console.log('🔥 COMPLETE RESET: Clearing ALL user-related data...');

    const results = [];

    // Clear session tables
    try {
      await db.execute(sql`DELETE FROM session`);
      results.push('✅ Cleared session table');
    } catch (error) {
      results.push(`❌ Error clearing session table: ${error.message}`);
    }

    try {
      await db.execute(sql`DELETE FROM user_sessions`);
      results.push('✅ Cleared user_sessions table');
    } catch (error) {
      results.push(`❌ Error clearing user_sessions table: ${error.message}`);
    }

    // Clear main users table
    try {
      await db.execute(sql`DELETE FROM users`);
      results.push('✅ Cleared users table');
    } catch (error) {
      results.push(`❌ Error clearing users table: ${error.message}`);
    }

    // Reset sequence for users table
    try {
      await db.execute(sql`ALTER SEQUENCE users_id_seq RESTART WITH 1`);
      results.push('✅ Reset users ID sequence');
    } catch (error) {
      results.push(`❌ Error resetting users sequence: ${error.message}`);
    }

    // Clear other user-related tables
    const userRelatedTables = [
      'user_payment_methods',
      'cart_items',
      'orders',
      'order_items',
      'purchases'
    ];

    for (const tableName of userRelatedTables) {
      try {
        await db.execute(sql.raw(`DELETE FROM ${tableName}`));
        results.push(`✅ Cleared ${tableName} table`);
      } catch (error) {
        results.push(`❌ Error clearing ${tableName}: ${error.message}`);
      }
    }

    console.log('🔥 COMPLETE RESET RESULTS:', results);

    res.json({
      message: 'Complete reset performed',
      results,
      warning: 'ALL user data has been permanently deleted!'
    });
  } catch (error) {
    console.error('Error performing complete reset:', error);
    res.status(500).json({ error: 'Failed to perform complete reset', details: error.message });
  }
});

// Admin endpoint to update the database trigger (GET for easy testing)
router.get('/api/admin/update-trigger', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    console.log('🔍 Admin check:', { userId: req.user?.id, isAdmin: req.user?.isAdmin });
    if (!req.user?.isAdmin) {
      return res.status(403).json({
        message: 'Admin access required',
        userId: req.user?.id,
        isAdmin: req.user?.isAdmin
      });
    }

    // Drop the existing trigger first
    await db.execute(sql`DROP TRIGGER IF EXISTS trigger_update_trust_score_on_identity_change ON users`);

    // Update the function with the new logic
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION update_trust_score_on_identity_change()
      RETURNS TRIGGER AS $$
      DECLARE
          old_fully_verified BOOLEAN := (OLD.identity_verified = true AND OLD.identity_verification_status = 'approved');
          new_fully_verified BOOLEAN := (NEW.identity_verified = true AND NEW.identity_verification_status = 'approved');
      BEGIN
          -- If verification state changed from not fully verified to fully verified
          IF old_fully_verified = false AND new_fully_verified = true THEN
              NEW.trust_score = LEAST(100, NEW.trust_score + 40);
              RAISE NOTICE 'Identity fully verified for user %: trust score increased by 40 points (% -> %)',
                  NEW.id, OLD.trust_score, NEW.trust_score;

          -- If verification state changed from fully verified to not fully verified
          ELSIF old_fully_verified = true AND new_fully_verified = false THEN
              NEW.trust_score = GREATEST(0, NEW.trust_score - 40);
              RAISE NOTICE 'Identity verification removed for user %: trust score decreased by 40 points (% -> %)',
                  NEW.id, OLD.trust_score, NEW.trust_score;
          END IF;

          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql
    `);

    // Recreate the trigger
    await db.execute(sql`
      CREATE TRIGGER trigger_update_trust_score_on_identity_change
          BEFORE UPDATE ON users
          FOR EACH ROW
          EXECUTE FUNCTION update_trust_score_on_identity_change()
    `);

    console.log('✅ Database trigger updated successfully');

    res.json({
      message: 'Database trigger updated successfully',
      success: true
    });

  } catch (error) {
    console.error('Error updating database trigger:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Note: In development mode, the /sell route is handled by Vite's catch-all middleware
// In production mode, it's handled by the serveStatic function in vite.ts

  return router;
}
