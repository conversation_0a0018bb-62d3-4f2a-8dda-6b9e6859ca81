import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq, sql } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Submit identity verification
router.post('/api/user/verify-identity', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userId = req.user.id;
    console.log('Processing identity verification submission for user:', userId);

    // Prepare verification data for submission (NOT auto-approved)
    const verificationData = {
      fullName: req.body.fullName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      address: req.body.address,
      submittedAt: new Date().toISOString()
      // Note: approvedAt will be set when admin manually approves
    };

    console.log('Submitting identity verification for review:', verificationData);

    // Get current user to check status
    const currentUser = await storage.getUser(userId);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`🔍 Identity Verification Submission:
      - User ID: ${userId}
      - Current Status: ${currentUser.identityVerificationStatus}
      - Already Verified: ${currentUser.identityVerified}`);

    // Check if user is already verified
    if (currentUser.identityVerified) {
      console.log(`⚠️ User ${userId} is already identity verified.`);
      return res.json({
        message: 'Identity verification already completed!',
        status: 'already_verified'
      });
    }

    // Check if user already has a pending verification
    if (currentUser.identityVerificationStatus === 'pending') {
      console.log(`⚠️ User ${userId} already has a pending identity verification.`);
      return res.json({
        message: 'Identity verification is already pending review.',
        status: 'pending'
      });
    }

    // Submit verification for review (NOT auto-approved)
    await storage.updateUser(userId, {
      identityVerificationStatus: 'pending',
      identityVerificationSubmittedAt: new Date(),
      identityVerificationData: verificationData
      // Note: identityVerified stays false until admin approval
      // Note: trustScore is NOT updated here - only when approved by admin
    });

    console.log(`✅ Identity verification submitted for user ${userId} - pending admin review`);

    res.json({
      message: 'Identity verification submitted successfully! Your submission is now pending review by our team.',
      status: 'pending'
    });

  } catch (error) {
    console.error('Error processing identity verification:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Use the trust score stored in the database (calculated by the rating system)
    // This follows the SELLER_TRUST_SCORE.md specification
    const trustScore = user.trustScore;

    // Get additional info for debugging
    let confirmedSales = 0;
    try {
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      confirmedSales = sellerPurchases.filter(p => p.status === 'received').length;
    } catch (error) {
      console.error('Error getting seller purchases for trust score:', error);
    }

    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

    // Debug logging
    console.log('🔍 Trust Score Status:', {
      userId: user?.id,
      trustScoreFromDB: user.trustScore,
      confirmedSales,
      hasIdentityVerification,
      identityVerificationStatus: user.identityVerificationStatus,
      identityVerified: user.identityVerified,
      identityVerificationApprovedAt: user.identityVerificationApprovedAt
    });

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    const response = {
      isVerified: true, // All logged-in users can list items
      isSeller: user.isSeller, // True if user has listed at least one item
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    };

    console.log('Seller status response:', JSON.stringify(response, null, 2));
    return res.json(response);

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to approve/reject identity verifications
router.post('/api/admin/identity-verification/:userId/:action', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { userId, action } = req.params;
    const userIdNum = parseInt(userId);

    if (!userIdNum || !['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: 'Invalid user ID or action' });
    }

    // Get the user
    const user = await storage.getUser(userIdNum);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user has a pending verification
    if (user.identityVerificationStatus !== 'pending') {
      return res.status(400).json({
        message: `User does not have a pending verification. Current status: ${user.identityVerificationStatus}`
      });
    }

    if (action === 'approve') {
      // Get user before update for comparison
      const userBefore = await storage.getUser(userIdNum);
      console.log('🔍 User BEFORE approval:', {
        id: userBefore?.id,
        identityVerified: userBefore?.identityVerified,
        identityVerificationStatus: userBefore?.identityVerificationStatus,
        trustScore: userBefore?.trustScore
      });

      // Approve the verification (trigger will automatically add +40 trust score)
      const updatedUser = await storage.updateUser(userIdNum, {
        identityVerified: true,
        identityVerificationStatus: 'approved',
        identityVerificationApprovedAt: new Date()
      });

      console.log('🔍 User AFTER approval:', {
        id: updatedUser?.id,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus,
        trustScore: updatedUser?.trustScore
      });

      console.log(`✅ Admin approved identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification approved successfully',
        status: 'approved',
        trustScoreIncrease: 40,
        updatedUser: updatedUser
      });
    } else {
      // Reject the verification
      await storage.updateUser(userIdNum, {
        identityVerificationStatus: 'rejected'
      });

      console.log(`❌ Admin rejected identity verification for user ${userIdNum}`);

      res.json({
        message: 'Identity verification rejected',
        status: 'rejected'
      });
    }

  } catch (error) {
    console.error('Error processing admin identity verification action:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Admin endpoint to list pending identity verifications
router.get('/api/admin/identity-verifications/pending', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    // Get all users with pending verifications
    const pendingUsers = await db.select({
      id: users.id,
      username: users.username,
      email: users.email,
      identityVerificationStatus: users.identityVerificationStatus,
      identityVerificationSubmittedAt: users.identityVerificationSubmittedAt,
      identityVerificationData: users.identityVerificationData
    })
    .from(users)
    .where(eq(users.identityVerificationStatus, 'pending'));

    res.json({
      pendingVerifications: pendingUsers,
      count: pendingUsers.length
    });

  } catch (error) {
    console.error('Error fetching pending identity verifications:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Debug endpoint to check user data directly
router.get('/api/debug/user/:userId', isAuthenticated, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    if (!userId) {
      return res.status(400).json({ message: 'Invalid user ID' });
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      id: user.id,
      username: user.username,
      trustScore: user.trustScore,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus,
      identityVerificationApprovedAt: user.identityVerificationApprovedAt,
      identityVerificationSubmittedAt: user.identityVerificationSubmittedAt
    });

  } catch (error) {
    console.error('Error fetching user debug data:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Debug endpoint to manually fix user 3's trust score
router.get('/api/debug/fix-user3-trust-score', async (req, res) => {
  try {
    // Get user 3
    const user = await storage.getUser(3);
    if (!user) {
      return res.status(404).json({ message: 'User 3 not found' });
    }

    console.log('🔍 User 3 BEFORE fix:', {
      id: user.id,
      trustScore: user.trustScore,
      identityVerified: user.identityVerified,
      identityVerificationStatus: user.identityVerificationStatus
    });

    // Check if user should have +40 trust score
    const shouldHaveBonus = user.identityVerified && user.identityVerificationStatus === 'approved';

    if (shouldHaveBonus && user.trustScore === 30) {
      // Update trust score to 70
      await storage.updateUser(3, { trustScore: 70 });

      const updatedUser = await storage.getUser(3);
      console.log('🔍 User 3 AFTER fix:', {
        id: updatedUser?.id,
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      });

      res.json({
        message: 'User 3 trust score fixed',
        before: { trustScore: 30 },
        after: { trustScore: updatedUser?.trustScore },
        reason: 'Identity verified and approved'
      });
    } else {
      res.json({
        message: 'No fix needed',
        currentTrustScore: user.trustScore,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        shouldHaveBonus
      });
    }

  } catch (error) {
    console.error('Error fixing user 3 trust score:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Debug endpoint to test "waiting for approval" state
router.get('/api/debug/set-user3-waiting-approval', async (req, res) => {
  try {
    // Set user 3 to have identityVerified=true but identityVerificationStatus='waiting_approval'
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'waiting_approval',
      trustScore: 30 // Keep at 30 to show no bonus yet
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 set to waiting approval state:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 set to waiting approval state',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error setting user 3 to waiting approval:', error);
    res.status(500).json({ error: 'Failed to set waiting approval state' });
  }
});

// Debug endpoint to get seller status for user 3 (to test UI logic)
router.get('/api/debug/user3-seller-status', async (req, res) => {
  try {
    const user = await storage.getUser(3);
    if (!user) {
      return res.status(404).json({ error: 'User 3 not found' });
    }

    // Calculate seller status like the real endpoint does
    const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
    const confirmedSales = sellerPurchases.filter(p => p.status === 'received');
    const trustScore = user.trustScore;
    const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus === 'approved';

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    const response = {
      isVerified: true,
      isSeller: user.isSeller,
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere,
      // Additional debug info
      user: {
        id: user.id,
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        trustScore: user.trustScore
      },
      uiState: {
        shouldShowWaitingForApproval: user.identityVerified && !user.identityVerificationStatus?.startsWith('app'),
        shouldShowStartVerification: !user.identityVerified || user.identityVerificationStatus === 'none',
        shouldShowCompleted: hasIdentityVerification
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting user 3 seller status:', error);
    res.status(500).json({ error: 'Failed to get seller status' });
  }
});

// Debug endpoint to approve user 3's identity verification
router.get('/api/debug/approve-user3', async (req, res) => {
  try {
    // Approve user 3's verification (this should trigger the +40 trust score)
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'approved',
      identityVerificationApprovedAt: new Date()
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 approved:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 identity verification approved',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error approving user 3:', error);
    res.status(500).json({ error: 'Failed to approve user 3' });
  }
});

// Debug endpoint to reset user 3 to not started verification state
router.get('/api/debug/reset-user3-verification', async (req, res) => {
  try {
    // Reset user 3 to have identityVerified=false and no status
    await storage.updateUser(3, {
      identityVerified: false,
      identityVerificationStatus: 'none',
      trustScore: 30 // Keep at 30 (no bonus)
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 reset to not started state:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'User 3 reset to not started verification state',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      }
    });
  } catch (error) {
    console.error('Error resetting user 3 verification:', error);
    res.status(500).json({ error: 'Failed to reset verification state' });
  }
});

// Debug endpoint to test sanitization with invalid status
router.get('/api/debug/test-sanitization', async (req, res) => {
  try {
    // Try to set user 3's status to an invalid value
    await storage.updateUser(3, {
      identityVerified: false,
      identityVerificationStatus: 'fdjfksjfds', // This should become 'none'
      trustScore: 30
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 after sanitization test:', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'Sanitization test completed',
      attempted: 'fdjfksjfds',
      actual: updatedUser?.identityVerificationStatus,
      sanitized: updatedUser?.identityVerificationStatus === 'none'
    });
  } catch (error) {
    console.error('Error testing sanitization:', error);
    res.status(500).json({ error: 'Failed to test sanitization' });
  }
});

// Debug endpoint to test the new sanitization logic (only 'app*' values allowed)
router.get('/api/debug/test-new-sanitization', async (req, res) => {
  try {
    const tests = [
      { value: 'approved', shouldBe: 'approved' }, // starts with 'app'
      { value: 'app_waiting', shouldBe: 'app_waiting' }, // starts with 'app'
      { value: 'application_review', shouldBe: 'application_review' }, // starts with 'app'
      { value: 'pending', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
      { value: 'rejected', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
      { value: 'random_text', shouldBe: 'none' }, // should be sanitized
      { value: 'fdjfksjfds', shouldBe: 'none' }, // should be sanitized
      { value: '', shouldBe: 'none' }, // empty string should be sanitized
      { value: 'none', shouldBe: 'none' }, // should be sanitized (doesn't start with 'app')
    ];

    const results = [];

    for (const test of tests) {
      await storage.updateUser(3, {
        identityVerificationStatus: test.value,
        trustScore: 30
      });

      const user = await storage.getUser(3);
      const result = {
        attempted: test.value,
        expected: test.shouldBe,
        actual: user?.identityVerificationStatus,
        correct: user?.identityVerificationStatus === test.shouldBe
      };
      results.push(result);
    }

    res.json({
      message: 'Validation tests completed',
      results,
      allPassed: results.every(r => r.correct)
    });
  } catch (error) {
    console.error('Error testing valid values:', error);
    res.status(500).json({ error: 'Failed to test valid values' });
  }
});

// Debug endpoint to test setting status to just 'app'
router.get('/api/debug/test-app-status', async (req, res) => {
  try {
    // Set user 3's status to just 'app' and identityVerified to true
    await storage.updateUser(3, {
      identityVerified: true,
      identityVerificationStatus: 'app',
      // Don't set trustScore - let the trigger handle it
    });

    const updatedUser = await storage.getUser(3);
    console.log('🔍 User 3 after setting status to "app":', {
      id: updatedUser?.id,
      trustScore: updatedUser?.trustScore,
      identityVerified: updatedUser?.identityVerified,
      identityVerificationStatus: updatedUser?.identityVerificationStatus
    });

    res.json({
      message: 'Set status to "app"',
      user: {
        trustScore: updatedUser?.trustScore,
        identityVerified: updatedUser?.identityVerified,
        identityVerificationStatus: updatedUser?.identityVerificationStatus
      },
      shouldHaveBonus: updatedUser?.identityVerified && updatedUser?.identityVerificationStatus === 'approved'
    });
  } catch (error) {
    console.error('Error testing app status:', error);
    res.status(500).json({ error: 'Failed to test app status' });
  }
});

// Debug endpoint to test various 'app*' values
router.get('/api/debug/test-app-variations', async (req, res) => {
  try {
    const testValues = ['app', 'approved', 'app_pending', 'application_review', 'app123'];
    const results = [];

    for (const value of testValues) {
      // Set the status
      await storage.updateUser(3, {
        identityVerified: true,
        identityVerificationStatus: value,
      });

      // Get the updated user
      const user = await storage.getUser(3);

      // Check seller status
      const sellerPurchases = await storage.getPurchasesBySellerId(user.id);
      const confirmedSales = sellerPurchases.filter(p => p.status === 'received');
      const hasIdentityVerification = user.identityVerified && user.identityVerificationStatus?.startsWith('app');

      results.push({
        testedValue: value,
        actualValue: user.identityVerificationStatus,
        trustScore: user.trustScore,
        hasIdentityVerification,
        shouldShowCompleted: hasIdentityVerification
      });
    }

    res.json({
      message: 'Tested various app* values',
      results,
      allWorking: results.every(r => r.hasIdentityVerification && r.trustScore === 70)
    });
  } catch (error) {
    console.error('Error testing app variations:', error);
    res.status(500).json({ error: 'Failed to test app variations' });
  }
});

// Admin endpoint to update the database trigger (GET for easy testing)
router.get('/api/admin/update-trigger', isAuthenticated, async (req, res) => {
  try {
    // Check if user is admin
    console.log('🔍 Admin check:', { userId: req.user?.id, isAdmin: req.user?.isAdmin });
    if (!req.user?.isAdmin) {
      return res.status(403).json({
        message: 'Admin access required',
        userId: req.user?.id,
        isAdmin: req.user?.isAdmin
      });
    }

    // Drop the existing trigger first
    await db.execute(sql`DROP TRIGGER IF EXISTS trigger_update_trust_score_on_identity_change ON users`);

    // Update the function with the new logic
    await db.execute(sql`
      CREATE OR REPLACE FUNCTION update_trust_score_on_identity_change()
      RETURNS TRIGGER AS $$
      DECLARE
          old_fully_verified BOOLEAN := (OLD.identity_verified = true AND OLD.identity_verification_status = 'approved');
          new_fully_verified BOOLEAN := (NEW.identity_verified = true AND NEW.identity_verification_status = 'approved');
      BEGIN
          -- If verification state changed from not fully verified to fully verified
          IF old_fully_verified = false AND new_fully_verified = true THEN
              NEW.trust_score = LEAST(100, NEW.trust_score + 40);
              RAISE NOTICE 'Identity fully verified for user %: trust score increased by 40 points (% -> %)',
                  NEW.id, OLD.trust_score, NEW.trust_score;

          -- If verification state changed from fully verified to not fully verified
          ELSIF old_fully_verified = true AND new_fully_verified = false THEN
              NEW.trust_score = GREATEST(0, NEW.trust_score - 40);
              RAISE NOTICE 'Identity verification removed for user %: trust score decreased by 40 points (% -> %)',
                  NEW.id, OLD.trust_score, NEW.trust_score;
          END IF;

          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql
    `);

    // Recreate the trigger
    await db.execute(sql`
      CREATE TRIGGER trigger_update_trust_score_on_identity_change
          BEFORE UPDATE ON users
          FOR EACH ROW
          EXECUTE FUNCTION update_trust_score_on_identity_change()
    `);

    console.log('✅ Database trigger updated successfully');

    res.json({
      message: 'Database trigger updated successfully',
      success: true
    });

  } catch (error) {
    console.error('Error updating database trigger:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});

// Note: In development mode, the /sell route is handled by Vite's catch-all middleware
// In production mode, it's handled by the serveStatic function in vite.ts

  return router;
}
