-- Update the database trigger to check both identity_verified AND identity_verification_status
-- Drop existing triggers first
DROP TRIGGER IF EXISTS trigger_update_trust_score_on_identity_change ON users;
DROP TRIGGER IF EXISTS sanitize_identity_verification_status_trigger ON users;
DROP FUNCTION IF EXISTS sanitize_identity_verification_status();

-- Create the function to sanitize identity_verification_status
CREATE OR REPLACE FUNCTION sanitize_identity_verification_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Sanitize identity_verification_status: only allow values starting with 'app'
    -- Everything else becomes 'none'
    IF NEW.identity_verification_status IS NOT NULL THEN
        IF NOT (NEW.identity_verification_status LIKE 'app%') THEN
            NEW.identity_verification_status := 'none';
        END IF;
    ELSE
        -- If NULL, set to 'none'
        NEW.identity_verification_status := 'none';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update the function with the new logic
CREATE OR REPLACE FUNCTION update_trust_score_on_identity_change()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    -- Check if identity verification status changed to fully approved
    -- Only add points when BOTH identity_verified = true AND identity_verification_status = 'approved'

    -- Calculate old and new verification states
    DECLARE
        old_fully_verified BOOLEAN := (OLD.identity_verified = true AND OLD.identity_verification_status = 'approved');
        new_fully_verified BOOLEAN := (NEW.identity_verified = true AND NEW.identity_verification_status = 'approved');
    BEGIN
        -- If verification state changed from not fully verified to fully verified
        IF old_fully_verified = false AND new_fully_verified = true THEN
            NEW.trust_score = LEAST(100, NEW.trust_score + 40);
            RAISE NOTICE 'Identity fully verified for user %: trust score increased by 40 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;

        -- If verification state changed from fully verified to not fully verified
        ELSIF old_fully_verified = true AND new_fully_verified = false THEN
            NEW.trust_score = GREATEST(0, NEW.trust_score - 40);
            RAISE NOTICE 'Identity verification removed for user %: trust score decreased by 40 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;
        END IF;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the sanitization trigger (runs first on INSERT and UPDATE)
CREATE TRIGGER sanitize_identity_verification_status_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION sanitize_identity_verification_status();

-- Recreate the trust score trigger (runs after sanitization on UPDATE only)
CREATE TRIGGER trigger_update_trust_score_on_identity_change
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_trust_score_on_identity_change();
