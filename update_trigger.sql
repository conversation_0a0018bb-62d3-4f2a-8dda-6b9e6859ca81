-- Update the database trigger to check both identity_verified AND identity_verification_status
-- Drop the existing trigger first
DROP TRIGGER IF EXISTS trigger_update_trust_score_on_identity_change ON users;

-- Update the function with the new logic
CREATE OR REPLACE FUNCTION update_trust_score_on_identity_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if identity verification status changed to fully approved
    -- Only add points when BOTH identity_verified = true AND identity_verification_status = 'approved'
    
    -- Calculate old and new verification states
    DECLARE
        old_fully_verified BOOLEAN := (OLD.identity_verified = true AND OLD.identity_verification_status = 'approved');
        new_fully_verified BOOLEAN := (NEW.identity_verified = true AND NEW.identity_verification_status = 'approved');
    BEGIN
        -- If verification state changed from not fully verified to fully verified
        IF old_fully_verified = false AND new_fully_verified = true THEN
            NEW.trust_score = LEAST(100, NEW.trust_score + 40);
            RAISE NOTICE 'Identity fully verified for user %: trust score increased by 40 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;

        -- If verification state changed from fully verified to not fully verified
        ELSIF old_fully_verified = true AND new_fully_verified = false THEN
            NEW.trust_score = GREATEST(0, NEW.trust_score - 40);
            RAISE NOTICE 'Identity verification removed for user %: trust score decreased by 40 points (% -> %)',
                NEW.id, OLD.trust_score, NEW.trust_score;
        END IF;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
CREATE TRIGGER trigger_update_trust_score_on_identity_change
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_trust_score_on_identity_change();
