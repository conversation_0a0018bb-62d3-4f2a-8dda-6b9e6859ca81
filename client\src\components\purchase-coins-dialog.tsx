import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DasWosCoinIcon from '@/components/shared/daswos-coin-icon';
import { formatDasWosCoins } from '@/lib/utils';

interface PurchaseCoinsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requiredAmount?: number;
  currentBalance?: number;
  onPurchaseComplete?: () => void;
}

const coinPackages = [
  { id: 1, amount: 1000, price: 10, name: "Starter Pack" },
  { id: 2, amount: 2500, price: 20, name: "Value Pack" },
  { id: 3, amount: 5000, price: 35, name: "Premium Pack" },
  { id: 4, amount: 10000, price: 60, name: "Ultimate Pack" },
];

const PurchaseCoinsDialog: React.FC<PurchaseCoinsDialogProps> = ({
  isOpen,
  onClose,
  requiredAmount = 0,
  currentBalance = 0,
  onPurchaseComplete
}) => {
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const shortfall = Math.max(0, requiredAmount - currentBalance);

  // Purchase coins mutation
  const purchaseMutation = useMutation({
    mutationFn: async (amount: number) => {
      const response = await fetch('/api/user/daswos-coins/purchase', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          metadata: {
            packageName: `${amount} DasWos Coins`,
            purchaseTimestamp: new Date().toISOString(),
            source: 'ai_buy_insufficient_funds'
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to purchase coins');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Coins Purchased Successfully!',
        description: `Your DasWos Coins have been added to your account.`,
      });

      // Refresh the balance
      queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

      // Call completion callback
      if (onPurchaseComplete) {
        onPurchaseComplete();
      }

      // Close dialog
      onClose();
    },
    onError: (error) => {
      console.error('Error purchasing coins:', error);
      toast({
        title: 'Purchase Failed',
        description: 'There was an error purchasing coins. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handlePurchase = () => {
    if (!selectedPackage) {
      toast({
        title: 'No package selected',
        description: 'Please select a coin package to purchase.',
        variant: 'destructive',
      });
      return;
    }

    const pkg = coinPackages.find(p => p.id === selectedPackage);
    if (pkg) {
      purchaseMutation.mutate(pkg.amount);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-2 mb-2">
          <DasWosCoinIcon size={24} />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Purchase DasWos Coins
          </h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {shortfall > 0 ? (
            <>You need {formatDasWosCoins(shortfall)} more DasWos Coins to complete this purchase.</>
          ) : (
            <>Purchase DasWos Coins to use for instant AI-powered shopping.</>
          )}
        </p>
      </div>

      <div className="space-y-3 mb-6">
        {coinPackages.map((pkg) => (
          <div
            key={pkg.id}
            className={`border rounded-lg p-3 cursor-pointer transition-colors ${
              selectedPackage === pkg.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
            onClick={() => setSelectedPackage(pkg.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedPackage === pkg.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300 dark:border-gray-600'
                }`}>
                  {selectedPackage === pkg.id && (
                    <div className="w-full h-full rounded-full bg-white scale-50"></div>
                  )}
                </div>
                <div>
                  <div className="font-medium">{pkg.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1">
                    <DasWosCoinIcon size={12} />
                    {formatDasWosCoins(pkg.amount)}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">${pkg.price}</div>
                <div className="text-xs text-gray-500">
                  {(pkg.price / pkg.amount * 100).toFixed(1)}¢ per coin
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex gap-3 justify-center">
        <Button
          variant="outline"
          onClick={onClose}
          className="px-6"
        >
          Cancel
        </Button>
        <Button
          onClick={handlePurchase}
          disabled={!selectedPackage || purchaseMutation.isPending}
          className="px-6"
        >
          {purchaseMutation.isPending ? 'Purchasing...' : 'Purchase Coins'}
        </Button>
      </div>
    </div>
  );
};

export default PurchaseCoinsDialog;
