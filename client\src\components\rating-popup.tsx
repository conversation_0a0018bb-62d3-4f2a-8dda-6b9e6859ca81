import React, { useState } from 'react';
import { Star, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';

interface RatingPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (rating: number, comment?: string) => Promise<void>;
  productTitle: string;
  sellerName: string;
}

export function RatingPopup({ isOpen, onClose, onSubmit, productTitle, sellerName }: RatingPopupProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (rating === 0) {
      toast({
        title: 'Rating Required',
        description: 'Please select a star rating before submitting.',
        variant: 'destructive',
      });
      return;
    }

    // Require comment for ratings of 2 stars or below
    if (rating <= 2 && !comment.trim()) {
      toast({
        title: 'Comment Required',
        description: 'Please provide feedback for ratings of 2 stars or below.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(rating, comment.trim() || undefined);
      toast({
        title: 'Rating Submitted',
        description: 'Thank you for your feedback!',
      });
      onClose();
      // Reset form
      setRating(0);
      setComment('');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit rating. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      // Reset form
      setRating(0);
      setComment('');
    }
  };

  if (!isOpen) return null;

  const getRatingText = (stars: number) => {
    switch (stars) {
      case 1: return 'Poor';
      case 2: return 'Fair';
      case 3: return 'Good';
      case 4: return 'Very Good';
      case 5: return 'Excellent';
      default: return 'Select Rating';
    }
  };

  const displayRating = hoveredRating || rating;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Rate Your Purchase</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-1">Product:</p>
          <p className="font-medium text-gray-900">{productTitle}</p>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-1">Seller:</p>
          <p className="font-medium text-gray-900">{sellerName}</p>
        </div>

        <div className="mb-6">
          <p className="text-sm font-medium text-gray-700 mb-3">How was your experience?</p>
          
          {/* Star Rating */}
          <div className="flex items-center gap-1 mb-2">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => setRating(star)}
                onMouseEnter={() => setHoveredRating(star)}
                onMouseLeave={() => setHoveredRating(0)}
                disabled={isSubmitting}
                className="p-1 disabled:opacity-50"
              >
                <Star
                  className={`h-8 w-8 ${
                    star <= displayRating
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  } transition-colors`}
                />
              </button>
            ))}
          </div>
          
          {/* Rating Text */}
          <p className="text-sm text-gray-600 mb-4">
            {displayRating > 0 ? getRatingText(displayRating) : 'Click to rate'}
          </p>

          {/* Comment Section */}
          <div className="space-y-2">
            <label htmlFor="comment" className="text-sm font-medium text-gray-700">
              {rating <= 2 ? 'Please tell us what went wrong (required):' : 'Additional comments (optional):'}
            </label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={
                rating <= 2
                  ? 'Please describe the issues you experienced...'
                  : 'Share your thoughts about this purchase...'
              }
              disabled={isSubmitting}
              className="min-h-[80px]"
              maxLength={500}
            />
            <p className="text-xs text-gray-500">{comment.length}/500 characters</p>
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || rating === 0}
            className="flex-1"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Rating'}
          </Button>
        </div>
      </div>
    </div>
  );
}
