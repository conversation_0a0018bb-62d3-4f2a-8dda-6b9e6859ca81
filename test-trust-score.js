#!/usr/bin/env node

/**
 * Test script to verify the seller trust score system is working correctly
 * This script simulates the rating process and checks if trust scores are updated properly
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testTrustScoreSystem() {
  console.log('🧪 Testing Seller Trust Score System');
  console.log('=====================================\n');

  try {
    // Test 1: Check current trust score for user "test"
    console.log('📊 Step 1: Checking current trust score...');

    // First, we need to login as the test user
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'test',
        password: 'test123'
      }),
      credentials: 'include'
    });

    if (!loginResponse.ok) {
      throw new Error('Failed to login as test user');
    }

    console.log('✅ Logged in as test user');

    // Get the session cookie
    const cookies = loginResponse.headers.get('set-cookie');

    // Check trust score
    const trustScoreResponse = await fetch(`${BASE_URL}/api/user/trust-score`, {
      headers: {
        'Cookie': cookies
      }
    });

    if (trustScoreResponse.ok) {
      const trustScoreData = await trustScoreResponse.json();
      console.log('📈 Current Trust Score:', trustScoreData);
    } else {
      console.log('❌ Failed to get trust score:', await trustScoreResponse.text());
    }

    // Test 2: Check seller status
    console.log('\n📊 Step 2: Checking seller status...');
    const sellerStatusResponse = await fetch(`${BASE_URL}/api/seller/status`, {
      headers: {
        'Cookie': cookies
      }
    });

    if (sellerStatusResponse.ok) {
      const sellerData = await sellerStatusResponse.json();
      console.log('🏪 Seller Status:', sellerData);
    } else {
      console.log('❌ Failed to get seller status:', await sellerStatusResponse.text());
    }

    // Test 3: Get user purchases to see if there are any to rate
    console.log('\n📊 Step 3: Checking user purchases...');
    const purchasesResponse = await fetch(`${BASE_URL}/api/user/purchases`, {
      headers: {
        'Cookie': cookies
      }
    });

    if (purchasesResponse.ok) {
      const purchases = await purchasesResponse.json();
      console.log('🛒 User Purchases:', purchases.length, 'purchases found');

      // Show recent purchases
      if (purchases.length > 0) {
        console.log('Recent purchases:');
        purchases.slice(0, 3).forEach((purchase, index) => {
          console.log(`  ${index + 1}. Purchase ID: ${purchase.id}, Status: ${purchase.status}, Rating: ${purchase.rating || 'Not rated'}`);
        });
      }
    } else {
      console.log('❌ Failed to get purchases:', await purchasesResponse.text());
    }

    console.log('\n✅ Trust Score System Test Complete!');
    console.log('\n📋 Summary:');
    console.log('- Trust scores are now managed exclusively by the rating system');
    console.log('- 2-star ratings should deduct 5 points as per SELLER_TRUST_SCORE.md');
    console.log('- Use the new /api/user/trust-score endpoint to check current scores');
    console.log('- Seller status endpoint now uses stored trust scores from database');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Instructions for manual testing
console.log('🔧 Manual Testing Instructions:');
console.log('==============================\n');
console.log('1. Start the server: npm run dev');
console.log('2. Login as user "test" with password "test123"');
console.log('3. Make a purchase or find an existing purchase');
console.log('4. Mark the purchase as received');
console.log('5. Submit a 2-star rating');
console.log('6. Check trust score at: GET /api/user/trust-score');
console.log('7. Verify that 5 points were deducted');
console.log('8. Check server console logs for detailed debugging info\n');

console.log('🌐 API Endpoints to test:');
console.log('- GET /api/user/trust-score - View current trust score');
console.log('- GET /api/seller/status - View seller status (uses stored trust score)');
console.log('- POST /api/user/purchases/{id}/rating - Submit rating (updates trust score)');
console.log('- POST /api/user/purchases/{id}/received - Mark as received (no longer updates trust score)\n');

// Run the test if this script is executed directly
testTrustScoreSystem();
